"""
Data Provider Agent - 数据提供代理
整合所有数据源，为其他Agent提供统一的数据接口
基于 Agno Framework 架构
"""

import asyncio
from typing import Dict, Any, List, Optional
import structlog
from datetime import datetime

try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    class Agent:
        pass

from ..tools.pool_scanner_tool import PoolScannerTool
from ..tools.supabase_db_tool import SupabaseDbTool
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)

class DataProviderAgent(Agent):
    """数据提供代理 - 统一管理所有数据源"""
    
    def __init__(self, model: Optional[Any] = None):
        if not AGNO_AVAILABLE:
            logger.warning("agno_framework_not_available",
                         message="Agno framework not installed, using fallback mode")
            # 不抛出异常，而是使用fallback模式
            self.agno_available = False
        else:
            self.agno_available = True

            # 使用本地Ollama模型而不是OpenAI
            super().__init__(
                name="DataProvider",
                role="DyFlow数据提供代理",
                agent_id="data-provider-agent",
                model=model or OpenAIChat(id="qwen2.5:3b"),  # 使用本地模型
                tools=[
                    PoolScannerTool(),
                    SupabaseDbTool()
                ],
                description="负责获取和管理BSC、Solana等链的实时池子数据",
                instructions=[
                    "你是DyFlow系统的数据提供代理",
                    "负责从多个数据源获取实时池子数据",
                    "确保数据质量和一致性",
                    "为其他Agent提供标准化的数据接口",
                    "监控数据源健康状态并处理异常情况"
                ],
                reasoning=False,  # 简化配置
                show_tool_calls=False,
                add_datetime_to_instructions=True
            )
        
        # 数据缓存
        self.data_cache = {
            'bsc_pools': [],
            'solana_pools': [],
            'last_update': None,
            'cache_ttl': 300  # 5分钟缓存
        }
        
        # 数据源配置
        self.data_sources = {
            'bsc': {
                'primary': 'pancakeswap_subgraph',
                'backup': 'enhanced_simulation'
            },
            'solana': {
                'primary': 'meteora_damm_v2',
                'backup': 'meteora_dlmm'
            }
        }
    
    async def get_real_time_pools(self, chains: List[str] = None, force_refresh: bool = False) -> Dict[str, Any]:
        """
        获取实时池子数据
        
        Args:
            chains: 要获取的链列表，默认 ['bsc', 'solana']
            force_refresh: 是否强制刷新缓存
            
        Returns:
            包含池子数据的标准化字典
        """
        if chains is None:
            chains = ['bsc', 'solana']
            
        try:
            # 检查缓存
            if not force_refresh and self._is_cache_valid():
                logger.info("returning_cached_data", chains=chains)
                return self._get_cached_data(chains)
            
            logger.info("fetching_fresh_data", chains=chains)
            
            # 获取新数据
            result = {
                'bsc_pools': [],
                'solana_pools': [],
                'metadata': {
                    'timestamp': get_utc_timestamp().isoformat(),
                    'chains_requested': chains,
                    'data_sources': self.data_sources
                }
            }
            
            # 并发获取各链数据
            tasks = []
            if 'bsc' in chains:
                tasks.append(self._fetch_bsc_data())
            if 'solana' in chains:
                tasks.append(self._fetch_solana_data())
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            task_index = 0
            if 'bsc' in chains:
                bsc_result = results[task_index]
                if isinstance(bsc_result, Exception):
                    logger.error("bsc_data_fetch_failed", error=str(bsc_result))
                    result['metadata']['bsc_error'] = str(bsc_result)
                    # 尝试从缓存获取
                    result['bsc_pools'] = self.data_cache.get('bsc_pools', [])
                else:
                    result['bsc_pools'] = bsc_result
                    self.data_cache['bsc_pools'] = bsc_result
                task_index += 1
            
            if 'solana' in chains:
                solana_result = results[task_index]
                if isinstance(solana_result, Exception):
                    logger.error("solana_data_fetch_failed", error=str(solana_result))
                    result['metadata']['solana_error'] = str(solana_result)
                    # 尝试从缓存获取
                    result['solana_pools'] = self.data_cache.get('solana_pools', [])
                else:
                    result['solana_pools'] = solana_result
                    self.data_cache['solana_pools'] = solana_result
            
            # 更新缓存时间戳
            self.data_cache['last_update'] = datetime.utcnow()
            
            # 更新元数据
            result['metadata'].update({
                'bsc_pools_count': len(result['bsc_pools']),
                'solana_pools_count': len(result['solana_pools']),
                'total_pools': len(result['bsc_pools']) + len(result['solana_pools']),
                'cache_updated': True
            })
            
            logger.info("real_time_data_updated",
                       bsc_pools=len(result['bsc_pools']),
                       solana_pools=len(result['solana_pools']))
            
            return result
            
        except Exception as e:
            logger.error("real_time_data_fetch_failed", error=str(e))
            # 返回缓存数据作为备用
            return self._get_cached_data(chains, error=str(e))
    
    async def _fetch_bsc_data(self) -> List[Dict[str, Any]]:
        """获取BSC池子数据"""
        try:
            # 使用PoolScannerTool获取BSC数据
            scanner = PoolScannerTool()
            result = await scanner.run('bsc', {
                'min_tvl': 10000,  # $10K最小TVL
                'max_pools': 100   # 最多100个池子
            })
            
            if result.get('error'):
                raise Exception(f"BSC数据获取失败: {result['error']}")
            
            pools = result.get('pools', [])
            
            # 转换为标准格式
            standardized_pools = []
            for pool in pools:
                standardized_pool = {
                    'pair': pool.get('pair_name', f"{pool.get('token0', '')}/{pool.get('token1', '')}"),
                    'protocol': 'PancakeSwap V3',
                    'address': pool.get('id', ''),
                    'chain': 'bsc',
                    'tvl_usd': pool.get('tvl_usd', 0),
                    'volume_24h': pool.get('volume_24h', 0),
                    'fees_24h': pool.get('fee24h', 0),
                    'apr': pool.get('fee_tvl', 0),  # fee_tvl就是年化收益率
                    'risk_level': self._calculate_risk_level(pool.get('fee_tvl', 0), pool.get('tvl_usd', 0)),
                    'fee_tier': f"{pool.get('fee_rate', 0) * 100:.3f}%",
                    'recommendation': self._get_recommendation(pool.get('fee_tvl', 0), pool.get('tvl_usd', 0))
                }
                standardized_pools.append(standardized_pool)
            
            return standardized_pools
            
        except Exception as e:
            logger.error("bsc_data_fetch_failed", error=str(e))
            raise
    
    async def _fetch_solana_data(self) -> List[Dict[str, Any]]:
        """获取Solana池子数据"""
        try:
            # 使用PoolScannerTool获取Solana数据
            scanner = PoolScannerTool()
            result = await scanner.run('solana', {
                'min_tvl': 1000,   # $1K最小TVL (Solana池子通常较小)
                'max_pools': 100   # 最多100个池子
            })
            
            if result.get('error'):
                raise Exception(f"Solana数据获取失败: {result['error']}")
            
            pools = result.get('pools', [])
            
            # 转换为标准格式
            standardized_pools = []
            for pool in pools:
                standardized_pool = {
                    'pair': pool.get('pair_name', f"{pool.get('token0', '')}-{pool.get('token1', '')}"),
                    'protocol': 'Meteora DAMM v2',
                    'address': pool.get('id', ''),
                    'chain': 'solana',
                    'tvl_usd': pool.get('tvl_usd', 0),
                    'volume_24h': pool.get('volume_24h', 0),
                    'fees_24h': pool.get('fee24h', 0),
                    'apr': pool.get('fee_tvl', 0),  # fee_tvl就是年化收益率
                    'risk_level': self._calculate_risk_level(pool.get('fee_tvl', 0), pool.get('tvl_usd', 0)),
                    'fee_tier': f"{pool.get('fee_rate', 0) * 100:.3f}%",
                    'recommendation': self._get_recommendation(pool.get('fee_tvl', 0), pool.get('tvl_usd', 0))
                }
                standardized_pools.append(standardized_pool)
            
            return standardized_pools
            
        except Exception as e:
            logger.error("solana_data_fetch_failed", error=str(e))
            raise
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self.data_cache.get('last_update'):
            return False
        
        cache_age = (datetime.utcnow() - self.data_cache['last_update']).total_seconds()
        return cache_age < self.data_cache['cache_ttl']
    
    def _get_cached_data(self, chains: List[str], error: str = None) -> Dict[str, Any]:
        """获取缓存数据"""
        result = {
            'bsc_pools': self.data_cache.get('bsc_pools', []) if 'bsc' in chains else [],
            'solana_pools': self.data_cache.get('solana_pools', []) if 'solana' in chains else [],
            'metadata': {
                'timestamp': get_utc_timestamp().isoformat(),
                'chains_requested': chains,
                'from_cache': True,
                'cache_age_seconds': (datetime.utcnow() - self.data_cache.get('last_update', datetime.utcnow())).total_seconds()
            }
        }
        
        if error:
            result['metadata']['error'] = error
            result['metadata']['fallback_to_cache'] = True
        
        result['metadata'].update({
            'bsc_pools_count': len(result['bsc_pools']),
            'solana_pools_count': len(result['solana_pools']),
            'total_pools': len(result['bsc_pools']) + len(result['solana_pools'])
        })
        
        return result
    
    def _calculate_risk_level(self, apr: float, tvl_usd: float) -> str:
        """计算风险等级"""
        if apr > 1000 or tvl_usd < 10000:
            return "高風險"
        elif apr > 100 or tvl_usd < 100000:
            return "中風險"
        else:
            return "低風險"
    
    def _get_recommendation(self, apr: float, tvl_usd: float) -> str:
        """获取投资建议"""
        if apr > 500 and tvl_usd > 50000:
            return "高收益機會"
        elif apr > 100 and tvl_usd > 100000:
            return "平衡選擇"
        elif tvl_usd > 1000000:
            return "穩定選擇"
        else:
            return "謹慎考慮"

# 导出
__all__ = ['DataProviderAgent', 'AGNO_AVAILABLE']
