"""
DyFlow 配置管理模块
负责加载和管理所有配置文件
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings

from .exceptions import InvalidConfigurationException


class AppConfig(BaseModel):
    """应用配置"""
    name: str = "DyFlow LP Agent"
    version: str = "1.0.0"
    check_interval: int = 300
    max_concurrent_operations: int = 3


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    file: str = "data/logs/dyflow.log"
    max_size: str = "10MB"
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = "sqlite:///data/state/dyflow.db"
    echo: bool = False
    pool_timeout: int = 30


class APIConfig(BaseModel):
    """API配置"""
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: int = 5
    rate_limit: int = 60


class CacheConfig(BaseModel):
    """缓存配置"""
    redis_url: str = "redis://localhost:6379/0"
    default_ttl: int = 300
    price_data_ttl: int = 60


class NetworkConfig(BaseModel):
    """网络配置"""
    name: str
    rpc_url: str
    chain_id: Optional[int] = None
    native_token: str
    explorer: str
    contracts: Dict[str, str] = Field(default_factory=dict)
    tokens: Dict[str, str] = Field(default_factory=dict)
    programs: Optional[Dict[str, str]] = None


class RiskManagementConfig(BaseModel):
    """风险管理配置"""
    max_slippage: float = 0.01
    emergency_exit_threshold: float = 0.10
    min_apr_threshold: float = 0.05
    max_position_size: float = 0.3
    stop_loss_threshold: float = 0.15
    price_change_limits: Dict[str, float] = Field(default_factory=lambda: {
        "warning": 0.05,
        "critical": 0.10,
        "emergency": 0.15
    })
    monitoring_window: int = 3600


class StrategyConfig(BaseModel):
    """策略配置"""
    min_tvl: float = 100000
    min_volume_24h: float = 50000
    max_pools_per_chain: int = 5
    preferred_tokens: list = Field(default_factory=lambda: [
        "BTC", "ETH", "BNB", "SOL", "USDT", "USDC", "BUSD"
    ])
    rebalance_threshold: float = 0.02
    gas_price_limit: Dict[str, float] = Field(default_factory=lambda: {
        "bsc": 20,
        "solana": 0.000005
    })
    allocation: Dict[str, float] = Field(default_factory=lambda: {
        "max_per_pool": 0.2,
        "reserve_ratio": 0.1,
        "cross_chain_ratio": 0.5
    })


class PoolConfig(BaseModel):
    """池子配置"""
    address: str
    name: str
    token_a: str
    token_b: str
    fee_tier: float
    priority: int
    min_liquidity: float


class Config:
    """主配置类"""
    
    def __init__(self):
        self._load_all_configs()
    
    @property
    def config_dir(self) -> Path:
        """配置文件目录"""
        return Path("config")

    def _load_yaml_file(self, filename: str) -> Dict[str, Any]:
        """加载YAML配置文件"""
        file_path = self.config_dir / filename
        if not file_path.exists():
            raise InvalidConfigurationException(f"配置文件不存在: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise InvalidConfigurationException(f"YAML解析错误 {filename}: {e}")
        except Exception as e:
            raise InvalidConfigurationException(f"读取配置文件失败 {filename}: {e}")

    def _load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 加载默认配置
            default_config = self._load_yaml_file("default.yaml")
            self.app = AppConfig(**default_config.get("app", {}))
            self.logging = LoggingConfig(**default_config.get("logging", {}))
            self.database = DatabaseConfig(**default_config.get("database", {}))
            self.api = APIConfig(**default_config.get("api", {}))
            self.cache = CacheConfig(**default_config.get("cache", {}))

            # 加载网络配置
            networks_config = self._load_yaml_file("networks.yaml")
            self.networks = {}
            for name, config in networks_config.items():
                # 将网络名称添加到配置中，但避免重复
                if 'name' not in config:
                    config = dict(config)
                    config['name'] = name
                self.networks[name] = NetworkConfig(**config)

            # 加载策略配置
            strategies_config = self._load_yaml_file("strategies.yaml")
            self.risk_management = RiskManagementConfig(
                **strategies_config.get("risk_management", {})
            )
            self.strategy = StrategyConfig(**strategies_config.get("strategy", {}))
            self.decision_weights = strategies_config.get("decision_weights", {})
            self.timing = strategies_config.get("timing", {})

            # 加载池子配置
            pools_config = self._load_yaml_file("pools.yaml")
            self.monitored_pools = {}
            for chain, pools in pools_config.get("monitored_pools", {}).items():
                self.monitored_pools[chain] = [
                    PoolConfig(**pool) for pool in pools
                ]
            self.blacklisted_tokens = pools_config.get("blacklisted_tokens", [])
            self.pool_selection_criteria = pools_config.get("pool_selection_criteria", {})
            self.auto_discovery = pools_config.get("auto_discovery", {})

        except Exception as e:
            raise InvalidConfigurationException(f"配置加载失败: {e}")

    def get_network_config(self, chain: str) -> NetworkConfig:
        """获取指定链的网络配置"""
        if chain not in self.networks:
            raise InvalidConfigurationException(f"未找到链配置: {chain}")
        return self.networks[chain]

    def get_monitored_pools(self, chain: str) -> list:
        """获取指定链的监控池子列表"""
        return self.monitored_pools.get(chain, [])

    def get_agent_config(self, agent_name: str, default: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取Agent配置"""
        if default is None:
            default = {}

        # 基础Agent配置
        agent_configs = {
            'planner': {
                'name': 'PlannerAgent',
                'role': 'LP策略规划',
                'model': 'qwen2.5:3b',
                'max_pools': 10,
                'min_tvl': 100000
            },
            'risk_sentinel': {
                'name': 'RiskSentinelAgent',
                'role': '风险监控',
                'model': 'qwen2.5:3b',
                'check_interval': 60,
                'alert_threshold': 0.05
            },
            'data_provider': {
                'name': 'DataProviderAgent',
                'role': '数据提供',
                'cache_ttl': 300,
                'update_interval': 30
            },
            'portfolio': {
                'name': 'PortfolioAgent',
                'role': '投资组合管理',
                'model': 'qwen2.5:3b',
                'rebalance_threshold': 0.02
            }
        }

        return agent_configs.get(agent_name, default)

    def is_token_blacklisted(self, token: str) -> bool:
        """检查代币是否在黑名单中"""
        return token.upper() in [t.upper() for t in self.blacklisted_tokens]

    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_chains = ["bsc", "solana"]
        for chain in required_chains:
            if chain not in self.networks:
                raise InvalidConfigurationException(f"缺少必需的链配置: {chain}")
        
        # 验证监控池子配置
        for chain in required_chains:
            pools = self.get_monitored_pools(chain)
            if not pools:
                raise InvalidConfigurationException(f"链 {chain} 没有配置监控池子")
        
        return True