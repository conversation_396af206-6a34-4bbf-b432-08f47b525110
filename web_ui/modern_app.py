#!/usr/bin/env python3
"""
DyFlow Modern Web UI Dashboard
使用Vue.js的現代化界面
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json
import aiohttp
import time

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
import uvicorn

# 導入項目配置
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.trading_config import (
    is_target_pair,
    get_agent_filter_config,
    get_target_pairs,
    API_CONFIG
)

# FastAPI應用
app = FastAPI(title="DyFlow Modern Dashboard", version="3.0")

# 靜態文件和模板
try:
    app.mount("/static", StaticFiles(directory="static"), name="static")
except:
    pass

templates = Jinja2Templates(directory="templates")

# WebSocket連接管理
websocket_connections: List[WebSocket] = []

class ModernDataProvider:
    """現代化數據提供者"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        
        # API配置
        self.meteora_api = "https://dlmm-api.meteora.ag"
        self.pancake_subgraph = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
        self.pancake_api_key = "9731921233db132a98c2325878e6c153"
        self.coingecko_api = "https://api.coingecko.com/api/v3"
        
        # 緩存
        self.cache = {}
        self.cache_ttl = 30  # 30秒緩存
        
    async def initialize(self):
        """初始化HTTP會話"""
        if not self.session:
            self.session = aiohttp.ClientSession()
    
    async def cleanup(self):
        """清理資源"""
        if self.session:
            await self.session.close()
    
    async def get_meteora_pools(self, limit: int = 25) -> List[Dict]:
        """獲取Meteora DLMM v1 + v2 真實池子數據"""
        try:
            cache_key = f"meteora_pools_{limit}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']

            await self.initialize()

            print(f"🔍 正在獲取Meteora DLMM v1 + v2 池子數據...")

            # 同時獲取 DLMM v1 和 v2 數據
            v1_pools = await self._get_meteora_v1_pools()
            v2_pools = await self._get_meteora_v2_pools()

            print(f"📊 Meteora v1池子: {len(v1_pools)}, v2池子: {len(v2_pools)}")

            # 合併v1和v2池子
            all_pools = v1_pools + v2_pools

            # 處理合併的池子數據
            formatted_pools = []
            processed_count = 0

            for pool in all_pools[:limit * 50]:  # 增加處理數量以找到更多有效池子
                try:
                    processed_count += 1

                    # 調試：顯示前幾個池子的原始數據結構
                    if processed_count <= 3:
                        print(f"🔍 Meteora池子 #{processed_count} 原始數據:")
                        print(f"   Keys: {list(pool.keys())}")
                        print(f"   Version: {pool.get('version', 'v1')}")

                    # 獲取池子基本信息
                    address = pool.get('address', '')
                    name = pool.get('name', '')
                    version = pool.get('version', 'v1')

                    # Meteora使用 "-" 分隔符，直接從name獲取
                    pair = None
                    if name and '-' in name:
                        # 將 "PAMBI-SOL" 轉換為 "PAMBI/SOL"
                        pair = name.replace('-', '/')
                        print(f"✅ Meteora {version}交易對: {name} -> {pair}")

                    # 如果沒有有效的交易對，跳過
                    if not pair or pair == '/' or 'UNK' in pair:
                        if processed_count <= 5:
                            print(f"❌ 跳過無效交易對: {name}")
                        continue

                    # 獲取Meteora的數值數據
                    tvl = 0
                    volume = 0
                    apr = 0
                    fees_24h = 0

                    # Meteora直接提供這些字段
                    try:
                        tvl = float(pool.get('liquidity', 0))
                        volume = float(pool.get('trade_volume_24h', 0))
                        apr = float(pool.get('apr', 0))

                        # 嘗試多種方式獲取手續費數據
                        fees_24h = 0

                        # 方式1: 直接從fees_24h字段
                        if 'fees_24h' in pool:
                            fees_24h = float(pool.get('fees_24h', 0))

                        # 方式2: 從today_fees字段
                        elif 'today_fees' in pool:
                            fees_24h = float(pool.get('today_fees', 0))

                        # 方式3: 從fee_tvl_ratio計算
                        elif 'fee_tvl_ratio' in pool and tvl > 0:
                            fee_ratio = float(pool.get('fee_tvl_ratio', 0))
                            fees_24h = (fee_ratio / 100) * tvl

                        # 方式4: 從交易量估算 (假設0.3%手續費)
                        elif volume > 0:
                            estimated_fee_rate = 0.003  # 0.3%
                            fees_24h = volume * estimated_fee_rate

                        if processed_count <= 5:
                            print(f"📊 {pair}: TVL=${tvl:,.0f}, Vol=${volume:,.0f}, APR={apr:.1f}%, Fees=${fees_24h:.2f}")

                    except (ValueError, TypeError) as e:
                        if processed_count <= 5:
                            print(f"❌ 數據轉換失敗 {pair}: {e}")
                        continue

                    # 只保留有真實數據的池子 - 參考DLMM網站，接受更小的TVL
                    if tvl < 5:  # 進一步降低門檻，DLMM網站顯示$10-$18K都是正常的
                        if processed_count <= 5:
                            print(f"❌ TVL太低 {pair}: ${tvl:,.0f}")
                        continue

                    # APR已經從API獲取，限制異常值
                    apr = min(apr, 1000)  # 限制最大APR避免異常值

                    # 風險評級
                    risk_level = "low"
                    if tvl < 1000:
                        risk_level = "high"
                    elif tvl < 10000:
                        risk_level = "medium"

                    formatted_pool = {
                        "id": address or f"meteora_{version}_{len(formatted_pools)}",
                        "chain": "solana",
                        "protocol": f"meteora_dlmm_{version}",
                        "pair": pair,
                        "tvl_usd": tvl,
                        "volume_24h": volume,
                        "apr": round(apr, 2),
                        "fees_24h": fees_24h,
                        "risk_level": risk_level,
                        "score": min(100, max(0, int(apr * 1.5 + (tvl / 50000)))),
                        "meteora_url": API_CONFIG["meteora"]["pool_url_template"].format(pool_id=address) if address else None,
                        "version": version
                    }
                    formatted_pools.append(formatted_pool)

                    if len(formatted_pools) >= limit * 2:  # 獲取足夠數量用於篩選
                        break

                except Exception as e:
                    continue

            print(f"✅ Meteora格式化池子數: {len(formatted_pools)}")

            # 緩存結果
            self.cache[cache_key] = {
                'data': formatted_pools,
                'timestamp': time.time()
            }

            return formatted_pools

        except Exception as e:
            print(f"❌ 獲取Meteora數據失敗: {e}")
            return []

    async def _get_meteora_v1_pools(self) -> List[Dict]:
        """獲取Meteora DLMM v1池子數據"""
        try:
            async with self.session.get(f"{self.meteora_api}/pair/all", timeout=30) as response:
                if response.status == 200:
                    data = await response.json()
                    pools = data if isinstance(data, list) else data.get('data', [])

                    # 標記為v1
                    for pool in pools:
                        pool['version'] = 'v1'

                    print(f"📊 Meteora v1池子數: {len(pools)}")
                    return pools
                else:
                    print(f"❌ Meteora v1 API請求失敗: {response.status}")
                    return []
        except Exception as e:
            print(f"❌ 獲取Meteora v1數據失敗: {e}")
            return []

    async def _get_meteora_v2_pools(self) -> List[Dict]:
        """獲取Meteora DLMM v2池子數據"""
        try:
            # 使用正確的Meteora DLMM v2 API端點
            v2_api_url = "https://dammv2-api.meteora.ag"
            endpoint = f"{v2_api_url}/pools"

            # 添加查詢參數以獲取更多數據 - 匹配damm.dlmm.me
            params = {
                "limit": 5000,  # 大幅增加獲取數量
                "order_by": "created_at_slot_timestamp",  # 按創建時間排序，獲取最新池子
                "order": "desc"
            }

            print(f"🔍 正在獲取Meteora DLMM v2數據: {endpoint}")

            async with self.session.get(endpoint, params=params, timeout=30) as response:
                print(f"📡 Meteora v2 API響應狀態: {response.status}")

                if response.status == 200:
                    data = await response.json()
                    print(f"📊 Meteora v2 API返回數據類型: {type(data)}")

                    # 根據OpenAPI文檔，響應格式為 {data: [...], status: int, ...}
                    if isinstance(data, dict) and 'data' in data:
                        pools = data['data']
                        print(f"📋 從data字段獲取v2池子數: {len(pools)}")
                    else:
                        pools = []
                        print(f"❌ 未找到data字段，響應結構: {list(data.keys()) if isinstance(data, dict) else type(data)}")

                    # 轉換v2數據格式以匹配v1格式
                    converted_pools = []
                    for pool in pools:
                        try:
                            # 根據OpenAPI文檔轉換字段名
                            converted_pool = {
                                'address': pool.get('pool_address', ''),
                                'name': f"{pool.get('token_a_symbol', 'UNK')}-{pool.get('token_b_symbol', 'UNK')}",
                                'liquidity': pool.get('tvl', 0),
                                'trade_volume_24h': pool.get('volume24h', 0),
                                'fees_24h': pool.get('fee24h', 0),
                                'apr': pool.get('apr', 0),
                                'fee_tvl_ratio': pool.get('fee_tvl_ratio', 0),
                                'created_at_slot_timestamp': pool.get('created_at_slot_timestamp', 0),
                                'created_at': pool.get('created_at_slot_timestamp', 0),  # 關鍵修復！
                                'version': 'v2',

                                # 保留原始v2字段
                                'token_a_symbol': pool.get('token_a_symbol', ''),
                                'token_b_symbol': pool.get('token_b_symbol', ''),
                                'token_a_mint': pool.get('token_a_mint', ''),
                                'token_b_mint': pool.get('token_b_mint', ''),
                                'pool_address': pool.get('pool_address', ''),
                                'tvl': pool.get('tvl', 0),
                                'volume24h': pool.get('volume24h', 0),
                                'fee24h': pool.get('fee24h', 0)
                            }
                            converted_pools.append(converted_pool)
                        except Exception as e:
                            print(f"❌ 轉換v2池子數據失敗: {e}")
                            continue

                    print(f"✅ Meteora v2池子數: {len(converted_pools)}")
                    return converted_pools
                else:
                    print(f"❌ Meteora v2 API請求失敗: {response.status}")
                    # 嘗試讀取錯誤響應
                    try:
                        error_text = await response.text()
                        print(f"❌ 錯誤詳情: {error_text[:200]}")
                    except:
                        pass
                    return []

        except Exception as e:
            print(f"❌ 獲取Meteora v2數據失敗: {e}")
            return []
    
    async def get_pancake_pools(self, limit: int = 25) -> List[Dict]:
        """獲取PancakeSwap真實池子數據 - 使用官方API獲取APR"""
        try:
            cache_key = f"pancake_pools_{limit}"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']

            await self.initialize()

            # 使用PancakeSwap官方API獲取池子數據（包含APR）
            api_url = "https://pancakeswap.finance/api/v3/pools"
            params = {
                "chainId": 56,  # BSC
                "page": 1,
                "size": limit,
                "orderBy": "tvlUSD",
                "orderDir": "desc"
            }

            headers = {
                "Accept": "application/json",
                "User-Agent": "DyFlow/1.0"
            }

            print(f"🔍 正在獲取PancakeSwap官方API數據: {api_url}")

            async with self.session.get(api_url, params=params, headers=headers) as response:
                print(f"📡 PancakeSwap API響應狀態: {response.status}")

                if response.status == 200:
                    result = await response.json()
                    print(f"📊 PancakeSwap API返回數據類型: {type(result)}")

                    # 處理不同的API響應格式
                    pools = []
                    if isinstance(result, dict):
                        if 'data' in result:
                            pools = result['data']
                        elif 'pools' in result:
                            pools = result['pools']
                        else:
                            pools = result.get('rows', [])
                    elif isinstance(result, list):
                        pools = result

                    print(f"📋 獲取到池子數: {len(pools)}")

                    if not pools:
                        # 如果官方API失敗，回退到Subgraph
                        print("⚠️ 官方API無數據，回退到Subgraph")
                        return await self._get_pancake_pools_subgraph(limit)

                    formatted_pools = []
                    for i, pool in enumerate(pools[:limit]):
                        print(f"🔍 PancakeSwap池子 #{i+1} 原始數據:")
                        print(f"   Keys: {list(pool.keys()) if isinstance(pool, dict) else 'Not a dict'}")

                        try:
                            # 嘗試不同的字段名稱
                            tvl_raw = float(pool.get('tvlUSD', pool.get('totalValueLockedUSD', pool.get('tvl', 0))))
                            volume_24h = float(pool.get('volume24hUSD', pool.get('volumeUSD', pool.get('volume_24h', 0))))
                            apr = float(pool.get('lpApr', pool.get('apr', pool.get('apy', 0))))

                            # 獲取交易對信息
                            if 'token0' in pool and 'token1' in pool:
                                token0_symbol = pool['token0'].get('symbol', 'UNK')
                                token1_symbol = pool['token1'].get('symbol', 'UNK')
                            else:
                                # 嘗試其他字段
                                token0_symbol = pool.get('token0Symbol', pool.get('baseToken', 'UNK'))
                                token1_symbol = pool.get('token1Symbol', pool.get('quoteToken', 'UNK'))

                            pair_name = f"{token0_symbol}/{token1_symbol}"

                            print(f"✅ PancakeSwap池子: {pair_name}")
                            print(f"📊 {pair_name}: TVL=${tvl_raw:,.0f}, Vol=${volume_24h:,.0f}, APR={apr:.1f}%")

                            # 智能TVL修復邏輯
                            tvl = self._fix_tvl_value(tvl_raw)

                            # 計算手續費
                            fee_tier = int(pool.get('feeTier', pool.get('fee', 3000)))  # 默認0.3%
                            fees_24h = volume_24h * (fee_tier / 1000000)

                            # 如果API沒有提供APR，則計算
                            if apr <= 0 and tvl > 0:
                                calculated_apr = (fees_24h / tvl) * 365 * 100
                                if 0 <= calculated_apr <= 50000:
                                    apr = calculated_apr
                                    print(f"🔧 計算APR: {apr:.2f}%")

                            # 風險評級
                            risk_level = "low"
                            if tvl < 100000:
                                risk_level = "high"
                            elif tvl < 1000000:
                                risk_level = "medium"

                            pool_id = pool.get('id', pool.get('address', ''))

                            # 使用配置文件的URL模板
                            pancake_url = None
                            if pool_id:
                                pancake_url = API_CONFIG["pancakeswap"]["pool_url_template"].format(pool_id=pool_id)

                            formatted_pool = {
                                "id": pool_id,
                                "chain": "bsc",
                                "protocol": "pancakeswap_v3",
                                "pair": pair_name,
                                "tvl_usd": tvl,
                                "volume_24h": volume_24h,
                                "fees_24h": fees_24h,
                                "apr": round(apr, 2),
                                "risk_level": risk_level,
                                "score": min(100, max(0, int(apr + (tvl / 100000)))),
                                "pancake_url": pancake_url
                            }
                            formatted_pools.append(formatted_pool)

                        except Exception as e:
                            print(f"❌ 處理池子數據失敗: {e}")
                            continue

                    # 緩存結果
                    self.cache[cache_key] = {
                        'data': formatted_pools,
                        'timestamp': time.time()
                    }

                    return formatted_pools
                else:
                    print(f"❌ PancakeSwap API請求失敗: {response.status}")
                    # 回退到Subgraph
                    return await self._get_pancake_pools_subgraph(limit)

                    
        except Exception as e:
            print(f"❌ 獲取PancakeSwap官方API失敗: {e}")
            # 回退到Subgraph
            return await self._get_pancake_pools_subgraph(limit)

    def _fix_tvl_value(self, tvl_raw: float) -> float:
        """修復TVL數值"""
        print(f"🔍 BSC池子原始TVL: {tvl_raw:.0f}")

        # 檢查是否為wei單位 (通常 > 1e18)
        if tvl_raw > 1e18:  # 超過1 quintillion，明顯是wei單位
            tvl = tvl_raw / 1e18  # 從wei轉換
            print(f"🔧 Wei轉換: {tvl_raw:.0f} -> ${tvl:,.0f}")
        # 檢查是否為其他大單位錯誤
        elif tvl_raw > 1e15:  # 超過1千萬億，可能是錯誤單位
            tvl = tvl_raw / 1e12  # 除以萬億
            print(f"🔧 大數值修復: {tvl_raw:.0f} -> ${tvl:,.0f}")
        # 檢查是否為百萬級錯誤
        elif tvl_raw > 1e12:  # 超過1萬億，可能是百萬級錯誤
            tvl = tvl_raw / 1e9   # 除以十億
            print(f"🔧 中數值修復: {tvl_raw:.0f} -> ${tvl:,.0f}")
        # 正常範圍，不修復
        else:
            tvl = tvl_raw
            if tvl > 0:
                print(f"✅ 正常TVL: ${tvl:,.0f}")

        return tvl

    def _fix_fee_value(self, fee_raw: float) -> float:
        """修復費用數值 - 處理wei單位或其他單位錯誤"""
        if fee_raw <= 0:
            return 0

        print(f"🔍 原始費用數值: {fee_raw:.0f}")

        # 檢查是否為wei單位 (通常 > 1e18)
        if fee_raw > 1e18:  # 超過1 quintillion，明顯是wei單位
            fee = fee_raw / 1e18  # 從wei轉換
            print(f"🔧 Wei轉換: {fee_raw:.0f} -> ${fee:,.2f}")
        # 檢查是否為其他大單位錯誤
        elif fee_raw > 1e15:  # 超過1千萬億，可能是錯誤單位
            fee = fee_raw / 1e12  # 除以萬億
            print(f"🔧 大數值修復: {fee_raw:.0f} -> ${fee:,.2f}")
        # 檢查是否為百萬級錯誤
        elif fee_raw > 1e12:  # 超過1萬億，可能是百萬級錯誤
            fee = fee_raw / 1e9   # 除以十億
            print(f"🔧 中數值修復: {fee_raw:.0f} -> ${fee:,.2f}")
        # 檢查是否為千級錯誤
        elif fee_raw > 1e9:   # 超過10億，可能是千級錯誤
            fee = fee_raw / 1e6   # 除以百萬
            print(f"🔧 小數值修復: {fee_raw:.0f} -> ${fee:,.2f}")
        # 正常範圍，不修復
        else:
            fee = fee_raw
            if fee > 0:
                print(f"✅ 正常費用: ${fee:,.2f}")

        return fee

    async def _get_pancake_pools_subgraph(self, limit: int = 25) -> List[Dict]:
        """回退方法：使用Subgraph獲取PancakeSwap數據"""
        try:
            print("🔄 使用Subgraph回退方法獲取PancakeSwap數據")

            # 根據PancakeSwap官方文檔修正的GraphQL查詢
            query = """
            query getTopPools($limit: Int!, $minTvl: String!) {
              pools(
                first: $limit
                orderBy: totalValueLockedUSD
                orderDirection: desc
                where: { totalValueLockedUSD_gt: $minTvl }
              ) {
                id
                token0 {
                  symbol
                  decimals
                }
                token1 {
                  symbol
                  decimals
                }
                feeTier
                volumeUSD
                totalValueLockedUSD
                feesUSD
                collectedFeesToken0
                collectedFeesToken1
                collectedFeesUSD
                createdAtTimestamp
                createdAtBlockNumber
              }
            }
            """

            variables = {
                "limit": limit,
                "minTvl": "10000"
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.pancake_api_key}"
            }

            payload = {
                "query": query,
                "variables": variables
            }

            async with self.session.post(self.pancake_subgraph, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    pools = result.get('data', {}).get('pools', [])

                    formatted_pools = []
                    for pool in pools:
                        try:
                            volume_24h = float(pool.get('volumeUSD', 0))
                            tvl_raw = float(pool.get('totalValueLockedUSD', 0))
                            fee_tier = int(pool.get('feeTier', 0))

                            # 修復TVL
                            tvl = self._fix_tvl_value(tvl_raw)

                            # 使用PancakeSwap V3 subgraph的新字段並修復單位問題
                            fees_24h = 0
                            apr = 0

                            # 獲取費用數據並修復單位問題
                            fees_usd_raw = float(pool.get('feesUSD', 0))
                            collected_fees_usd_raw = float(pool.get('collectedFeesUSD', 0))

                            print(f"🔍 BSC池子原始費用數據: feesUSD={fees_usd_raw:.0f}, collectedFeesUSD={collected_fees_usd_raw:.0f}")

                            # 修復費用數據的單位問題（可能是wei單位）
                            fees_usd = self._fix_fee_value(fees_usd_raw)
                            collected_fees_usd = self._fix_fee_value(collected_fees_usd_raw)

                            print(f"🔧 BSC池子修復後費用: feesUSD=${fees_usd:.2f}, collectedFeesUSD=${collected_fees_usd:.2f}")

                            if fees_usd > 0:
                                # 使用修復後的feesUSD字段
                                fees_24h = fees_usd
                                if tvl > 0:
                                    apr = (fees_24h / tvl) * 365 * 100
                                    print(f"✅ 使用修復feesUSD計算APR: {apr:.2f}%")
                            elif collected_fees_usd > 0:
                                # 使用修復後的collectedFeesUSD字段
                                fees_24h = collected_fees_usd
                                if tvl > 0:
                                    apr = (fees_24h / tvl) * 365 * 100
                                    print(f"✅ 使用修復collectedFeesUSD計算APR: {apr:.2f}%")
                            else:
                                # 回退到基於交易量的計算
                                if tvl > 0 and volume_24h > 0:
                                    # 使用更保守的手續費計算
                                    fee_rate = fee_tier / 1000000  # 將feeTier轉換為小數
                                    estimated_fees = volume_24h * fee_rate
                                    fees_24h = estimated_fees
                                    apr = (estimated_fees / tvl) * 365 * 100
                                    print(f"⚠️ 使用估算計算: Vol=${volume_24h:.0f}, FeeRate={fee_rate:.4f}, Fees=${fees_24h:.2f}, APR={apr:.2f}%")

                            # 限制APR在合理範圍內 (0.01% - 10000%)
                            if apr > 10000 or apr < 0:
                                print(f"⚠️ APR異常，已重置: {apr:.1f}% -> 0%")
                                apr = 0
                            elif apr > 0 and apr < 0.01:
                                print(f"⚠️ APR太低，已重置: {apr:.4f}% -> 0%")
                                apr = 0

                            # 風險評級
                            risk_level = "low"
                            if tvl < 100000:
                                risk_level = "high"
                            elif tvl < 1000000:
                                risk_level = "medium"

                            pool_id = pool.get('id', '')
                            token0_symbol = pool.get('token0', {}).get('symbol', 'UNK')
                            token1_symbol = pool.get('token1', {}).get('symbol', 'UNK')
                            pair_name = f"{token0_symbol}/{token1_symbol}"

                            # 使用配置文件的URL模板
                            pancake_url = None
                            if pool_id:
                                pancake_url = API_CONFIG["pancakeswap"]["pool_url_template"].format(pool_id=pool_id)

                            formatted_pool = {
                                "id": pool_id,
                                "chain": "bsc",
                                "protocol": "pancakeswap_v3",
                                "pair": pair_name,
                                "tvl_usd": tvl,
                                "volume_24h": volume_24h,
                                "fees_24h": fees_24h,
                                "apr": round(apr, 2),
                                "risk_level": risk_level,
                                "score": min(100, max(0, int(apr + (tvl / 100000)))),
                                "pancake_url": pancake_url
                            }
                            formatted_pools.append(formatted_pool)
                        except Exception as e:
                            continue

                    return formatted_pools
                else:
                    print(f"❌ Subgraph請求失敗: {response.status}")
                    return []

        except Exception as e:
            print(f"❌ Subgraph回退方法失敗: {e}")
            return []
    
    async def get_token_prices(self) -> Dict[str, float]:
        """獲取代幣價格"""
        try:
            cache_key = "token_prices"
            if self._is_cached(cache_key):
                return self.cache[cache_key]['data']
            
            await self.initialize()
            
            # 獲取主要代幣價格
            url = f"{self.coingecko_api}/simple/price"
            params = {
                "ids": "solana,binancecoin,ethereum,bitcoin,usd-coin,tether",
                "vs_currencies": "usd"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    prices = {
                        "SOL": data.get('solana', {}).get('usd', 0),
                        "BNB": data.get('binancecoin', {}).get('usd', 0),
                        "ETH": data.get('ethereum', {}).get('usd', 0),
                        "BTC": data.get('bitcoin', {}).get('usd', 0),
                        "USDC": data.get('usd-coin', {}).get('usd', 0),
                        "USDT": data.get('tether', {}).get('usd', 0)
                    }
                    
                    # 緩存結果
                    self.cache[cache_key] = {
                        'data': prices,
                        'timestamp': time.time()
                    }
                    
                    return prices
                    
        except Exception as e:
            print(f"獲取代幣價格失敗: {e}")
            return {}
    
    def _is_cached(self, key: str) -> bool:
        """檢查緩存是否有效"""
        if key not in self.cache:
            return False
        
        cache_time = self.cache[key]['timestamp']
        return (time.time() - cache_time) < self.cache_ttl

class ModernDashboardData:
    """現代化Dashboard數據管理"""
    
    def __init__(self):
        self.data_provider = ModernDataProvider()
        self.system_status = {
            "supervisor": {
                "is_running": True,
                "agno_enabled": False,
                "uptime_seconds": 3600
            },
            "connections": {
                "meteora_api": "connected",
                "pancakeswap_api": "connected", 
                "coingecko_api": "connected"
            }
        }
        
        # 真實數據
        self.pool_data = []
        self.token_prices = {}
        self.positions = [
            {
                "id": "pos_001",
                "pool": "SOL/USDC",
                "chain": "solana",
                "liquidity_usd": 5000,
                "pnl_pct": 8.5,
                "il_pct": -2.1,
                "status": "active",
                "range": "±12.5%",
                "apr": 15.2
            },
            {
                "id": "pos_002", 
                "pool": "BNB/USDT",
                "chain": "bsc",
                "liquidity_usd": 8000,
                "pnl_pct": 12.3,
                "il_pct": -1.8,
                "status": "active",
                "range": "±12.5%",
                "apr": 12.8
            },
            {
                "id": "pos_003",
                "pool": "PEPE/SOL", 
                "chain": "solana",
                "liquidity_usd": 2500,
                "pnl_pct": -5.2,
                "il_pct": -6.8,
                "status": "monitoring",
                "range": "±12.5%",
                "apr": 45.6
            }
        ]
        
        self.risk_alerts = []
        self.agent_logs = []
        self.last_update = datetime.now()
    
    async def update_real_data(self):
        """更新真實數據"""
        try:
            print(f"🔄 更新真實數據... {datetime.now().strftime('%H:%M:%S')}")
            
            # 並發獲取數據
            tasks = [
                self.data_provider.get_meteora_pools(15),
                self.data_provider.get_pancake_pools(15),
                self.data_provider.get_token_prices()
            ]
            
            meteora_pools, pancake_pools, token_prices = await asyncio.gather(*tasks)
            
            # 安全合併池子數據
            meteora_pools = meteora_pools or []
            pancake_pools = pancake_pools or []
            token_prices = token_prices or {}

            # Agent篩選邏輯
            filtered_meteora = self._agent_filter_pools(meteora_pools, "solana")
            filtered_pancake = self._agent_filter_pools(pancake_pools, "bsc")

            self.pool_data = filtered_meteora + filtered_pancake
            self.token_prices = token_prices

            # 更新系統狀態
            self.system_status["connections"]["meteora_api"] = "connected" if meteora_pools else "error"
            self.system_status["connections"]["pancakeswap_api"] = "connected" if pancake_pools else "error"
            self.system_status["connections"]["coingecko_api"] = "connected" if token_prices else "error"
            
            # 添加實時日誌
            self.agent_logs.insert(0, {
                "timestamp": datetime.now(),
                "agent": "DataProvider",
                "level": "info",
                "message": f"數據更新完成: Meteora {len(meteora_pools)}池, BSC {len(pancake_pools)}池, 價格 {len(token_prices)}個"
            })
            
            # 保留最近20條日誌
            self.agent_logs = self.agent_logs[:20]
            
            self.last_update = datetime.now()
            
            print(f"✅ 數據更新完成: 總共 {len(self.pool_data)} 個池子")
            
        except Exception as e:
            print(f"❌ 數據更新失敗: {e}")
            
            # 添加錯誤日誌
            self.agent_logs.insert(0, {
                "timestamp": datetime.now(),
                "agent": "DataProvider",
                "level": "error",
                "message": f"數據更新失敗: {str(e)}"
            })
    
    def _agent_filter_pools(self, pools: List[Dict], chain: str) -> List[Dict]:
        """Agent篩選池子邏輯 - 差異化篩選策略"""
        if not pools:
            return []

        # 獲取配置
        filter_config = get_agent_filter_config(chain)
        filtered_pools = []

        strategy = filter_config.get('strategy', 'balanced')
        use_strict_safety = filter_config.get('use_strict_safety', False)

        print(f"🔍 {strategy.upper()}策略Agent篩選 {chain.upper()} 池子，原始數量: {len(pools)}")

        processed_count = 0
        for pool in pools:
            processed_count += 1
            try:
                # 獲取池子信息
                pair = pool.get('pair', '').strip()
                tvl = pool.get('tvl_usd', 0)
                apr = pool.get('apr', 0)
                volume_24h = pool.get('volume_24h', 0)
                fees_24h = pool.get('fees_24h', 0)
                created_at = pool.get('created_at', None)

                # 檢查是否為目標交易對
                is_target = is_target_pair(pair, chain)
                if not is_target:
                    if processed_count <= 5:
                        print(f"❌ 非目標交易對: {pair} ({chain})")
                    continue

                print(f"✅ 目標交易對: {pair} (TVL: ${tvl:.0f}, APR: {apr:.1f}%, Fees: ${fees_24h:.2f})")

                # 根據鏈選擇不同的篩選策略
                if use_strict_safety and chain == 'solana':
                    # SOL使用嚴格安全性篩選
                    if not self._apply_strict_safety_filter(pool, filter_config, pair):
                        continue
                else:
                    # BSC使用平衡篩選
                    if not self._apply_balanced_filter(pool, filter_config, pair):
                        continue

                # 計算評分
                safety_score = self._calculate_safety_score(pool, chain)
                agent_score = self._calculate_comprehensive_score(pool, chain, safety_score)
                pool['agent_score'] = agent_score
                pool['safety_score'] = safety_score

                if agent_score >= filter_config['min_agent_score']:
                    filtered_pools.append(pool)
                    print(f"🎯 池子通過篩選: {pair} (安全: {safety_score}, 綜合: {agent_score})")
                else:
                    print(f"❌ 綜合評分不足: {pair} (評分: {agent_score}, 需要: {filter_config['min_agent_score']})")

            except Exception as e:
                print(f"❌ 處理池子時出錯: {e}")
                continue

        # 按策略排序
        if use_strict_safety:
            # 安全性優先排序
            filtered_pools.sort(key=lambda x: (x.get('safety_score', 0), x.get('agent_score', 0)), reverse=True)
        else:
            # 綜合評分排序
            filtered_pools.sort(key=lambda x: x.get('agent_score', 0), reverse=True)

        max_pools = filter_config['max_pools']
        result = filtered_pools[:max_pools]

        print(f"🏁 {chain.upper()} {strategy}篩選完成: {len(result)} 個池子")
        return result

    def _calculate_agent_score(self, pool: Dict, chain: str) -> int:
        """計算Agent評分"""
        try:
            tvl = pool.get('tvl_usd', 0)
            apr = pool.get('apr', 0)
            volume_24h = pool.get('volume_24h', 0)
            risk_level = pool.get('risk_level', 'high')

            score = 0

            # TVL評分 (30分)
            if tvl >= 1000000:
                score += 30
            elif tvl >= 500000:
                score += 25
            elif tvl >= 100000:
                score += 20
            elif tvl >= 50000:
                score += 15

            # APR評分 (25分)
            if 10 <= apr <= 50:
                score += 25  # 最佳APR範圍
            elif 5 <= apr <= 100:
                score += 20
            elif apr > 0:
                score += 10

            # 交易量評分 (20分)
            if volume_24h >= 100000:
                score += 20
            elif volume_24h >= 50000:
                score += 15
            elif volume_24h >= 10000:
                score += 10
            elif volume_24h >= 1000:
                score += 5

            # 風險評分 (15分)
            if risk_level == 'low':
                score += 15
            elif risk_level == 'medium':
                score += 10
            elif risk_level == 'high':
                score += 5

            # 鏈特定加分 (10分)
            if chain == "solana":
                # Solana生態加分
                pair = pool.get('pair', '').upper()
                if 'SOL' in pair or 'USDC' in pair:
                    score += 10
                elif 'USDT' in pair:
                    score += 5
            elif chain == "bsc":
                # BSC生態加分
                pair = pool.get('pair', '').upper()
                if 'BNB' in pair or 'USDT' in pair:
                    score += 10
                elif 'BUSD' in pair or 'USDC' in pair:
                    score += 5

            return min(100, max(0, score))  # 限制在0-100範圍

        except Exception as e:
            return 0

    def _calculate_safety_score(self, pool: Dict, chain: str) -> int:
        """計算安全性評分 - 第一重點"""
        try:
            tvl = pool.get('tvl_usd', 0)
            volume_24h = pool.get('volume_24h', 0)
            fees_24h = pool.get('fees_24h', 0)
            pair = pool.get('pair', '').upper()

            safety_score = 0

            # 流動性安全性 (30分)
            if tvl >= 100000:
                safety_score += 30  # 高流動性，安全
            elif tvl >= 10000:
                safety_score += 20  # 中等流動性
            elif tvl >= 1000:
                safety_score += 10  # 低流動性
            elif tvl >= 10:
                safety_score += 5   # 最低可接受

            # 交易活躍度安全性 (25分)
            if volume_24h >= 50000:
                safety_score += 25  # 高活躍度
            elif volume_24h >= 10000:
                safety_score += 20  # 中等活躍度
            elif volume_24h >= 1000:
                safety_score += 15  # 低活躍度
            elif volume_24h >= 100:
                safety_score += 10  # 最低活躍度

            # 手續費收入安全性 (20分)
            if fees_24h >= 100:
                safety_score += 20  # 高手續費收入
            elif fees_24h >= 50:
                safety_score += 15  # 中等手續費收入
            elif fees_24h >= 10:
                safety_score += 10  # 低手續費收入
            elif fees_24h >= 5:
                safety_score += 5   # 最低手續費收入

            # 基礎代幣安全性 (15分)
            safe_tokens = {
                'bsc': ['BNB', 'WBNB', 'USDT', 'USDC', 'BUSD'],
                'solana': ['SOL', 'WSOL', 'USDC', 'USDT']
            }

            if chain in safe_tokens:
                for safe_token in safe_tokens[chain]:
                    if safe_token in pair:
                        safety_score += 15
                        break

            # 風險分散性 (10分)
            # 如果是主流交易對組合，給予額外安全分
            if chain == 'bsc':
                if any(combo in pair for combo in ['BNB/USDT', 'BNB/USDC', 'USDT/USDC']):
                    safety_score += 10
            elif chain == 'solana':
                if any(combo in pair for combo in ['SOL/USDC', 'SOL/USDT']):
                    safety_score += 10

            return min(100, max(0, safety_score))

        except Exception as e:
            return 0

    def _calculate_comprehensive_score(self, pool: Dict, chain: str, safety_score: int) -> int:
        """計算綜合評分 - 結合安全性和收益性"""
        try:
            tvl = pool.get('tvl_usd', 0)
            apr = pool.get('apr', 0)
            volume_24h = pool.get('volume_24h', 0)
            fees_24h = pool.get('fees_24h', 0)

            # 基礎分數從安全性評分開始
            score = safety_score * 0.6  # 安全性佔60%

            # 收益性評分 (40%)
            profitability_score = 0

            # APR收益性 (20分)
            if 5 <= apr <= 50:
                profitability_score += 20  # 理想APR範圍
            elif 1 <= apr <= 100:
                profitability_score += 15  # 可接受APR範圍
            elif apr > 0:
                profitability_score += 10  # 有收益

            # Fee/TVL比率收益性 (10分)
            if tvl > 0:
                fee_tvl_ratio = (fees_24h / tvl) * 100
                if fee_tvl_ratio >= 10:
                    profitability_score += 10  # 高收益率
                elif fee_tvl_ratio >= 5:
                    profitability_score += 8   # 中等收益率
                elif fee_tvl_ratio >= 1:
                    profitability_score += 5   # 低收益率

            # 流動性效率 (10分)
            if tvl > 0 and volume_24h > 0:
                turnover_ratio = volume_24h / tvl
                if turnover_ratio >= 1:
                    profitability_score += 10  # 高效率
                elif turnover_ratio >= 0.5:
                    profitability_score += 8   # 中等效率
                elif turnover_ratio >= 0.1:
                    profitability_score += 5   # 低效率

            score += profitability_score * 0.4  # 收益性佔40%

            return min(100, max(0, int(score)))

        except Exception as e:
            return safety_score  # 如果計算失敗，返回安全性評分

    def _apply_strict_safety_filter(self, pool: Dict, filter_config: Dict, pair: str) -> bool:
        """應用嚴格安全性篩選 - 僅用於SOL"""
        tvl = pool.get('tvl_usd', 0)
        fees_24h = pool.get('fees_24h', 0)
        created_at = pool.get('created_at', None)

        failure_reasons = []

        # TVL >= $10
        if tvl < filter_config['min_tvl_usd']:
            failure_reasons.append(f"TVL太低: ${tvl:.2f} < ${filter_config['min_tvl_usd']}")

        # Created within 2 days (如果有創建時間數據)
        if created_at and 'max_pool_age_days' in filter_config:
            try:
                from datetime import datetime
                if isinstance(created_at, str):
                    created_date = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                else:
                    created_date = datetime.fromtimestamp(created_at)

                days_old = (datetime.now() - created_date).days
                if days_old > filter_config['max_pool_age_days']:
                    failure_reasons.append(f"池子太舊: {days_old}天 > {filter_config['max_pool_age_days']}天")
            except:
                # 如果無法解析創建時間，跳過此檢查
                pass

        # Fee/TVL >= 5.00%
        if tvl > 0 and 'min_fee_tvl_ratio' in filter_config:
            fee_tvl_ratio = (fees_24h / tvl) * 100
            if fee_tvl_ratio < filter_config['min_fee_tvl_ratio']:
                failure_reasons.append(f"Fee/TVL比率太低: {fee_tvl_ratio:.2f}% < {filter_config['min_fee_tvl_ratio']}%")

        # 24h Fees > $5
        if 'min_daily_fees' in filter_config and fees_24h < filter_config['min_daily_fees']:
            failure_reasons.append(f"24h手續費太低: ${fees_24h:.2f} < ${filter_config['min_daily_fees']}")

        if failure_reasons:
            print(f"❌ SOL嚴格篩選不通過: {pair} - {'; '.join(failure_reasons)}")
            return False

        return True

    def _apply_balanced_filter(self, pool: Dict, filter_config: Dict, pair: str) -> bool:
        """應用平衡篩選 - 用於BSC"""
        tvl = pool.get('tvl_usd', 0)
        apr = pool.get('apr', 0)
        volume_24h = pool.get('volume_24h', 0)
        risk_level = pool.get('risk_level', 'high')

        failure_reasons = []

        # 基本篩選條件
        if tvl < filter_config['min_tvl_usd']:
            failure_reasons.append(f"TVL太低: ${tvl:.0f} < ${filter_config['min_tvl_usd']}")

        if apr < filter_config['min_apr'] or apr > filter_config['max_apr']:
            failure_reasons.append(f"APR超出範圍: {apr:.1f}% (範圍: {filter_config['min_apr']}-{filter_config['max_apr']}%)")

        if volume_24h < filter_config['min_volume_24h']:
            failure_reasons.append(f"交易量太低: ${volume_24h:.0f} < ${filter_config['min_volume_24h']}")

        if risk_level not in filter_config['allowed_risk_levels']:
            failure_reasons.append(f"風險等級不符: {risk_level} (允許: {filter_config['allowed_risk_levels']})")

        if failure_reasons:
            print(f"❌ BSC平衡篩選不通過: {pair} - {'; '.join(failure_reasons)}")
            return False

        return True

    async def cleanup(self):
        """清理資源"""
        await self.data_provider.cleanup()

# 全局Dashboard數據
dashboard_data = ModernDashboardData()

@app.on_event("startup")
async def startup_event():
    """應用啟動事件"""
    print("🚀 DyFlow Modern Web UI 啟動中...")
    
    # 初始化數據提供者
    await dashboard_data.data_provider.initialize()
    
    # 立即獲取一次數據
    await dashboard_data.update_real_data()
    
    # 啟動數據更新任務
    asyncio.create_task(update_modern_dashboard_data())
    
    print("✅ DyFlow Modern Web UI 已啟動")
    print("📱 訪問 http://localhost:8082 查看現代化Dashboard")

@app.on_event("shutdown")
async def shutdown_event():
    """應用關閉事件"""
    await dashboard_data.cleanup()

async def update_modern_dashboard_data():
    """定期更新現代化Dashboard數據"""
    while True:
        try:
            await dashboard_data.update_real_data()
            
            # 廣播更新到所有WebSocket連接
            if websocket_connections:
                data = {
                    "type": "dashboard_update",
                    "data": {
                        "system_status": dashboard_data.system_status,
                        "pool_data": dashboard_data.pool_data,
                        "positions": dashboard_data.positions,
                        "risk_alerts": dashboard_data.risk_alerts,
                        "agent_logs": dashboard_data.agent_logs[:10],
                        "token_prices": dashboard_data.token_prices,
                        "last_update": dashboard_data.last_update.isoformat()
                    }
                }
                
                # 發送到所有連接的客戶端
                disconnected = []
                for websocket in websocket_connections:
                    try:
                        await websocket.send_text(json.dumps(data, default=str))
                    except:
                        disconnected.append(websocket)
                
                # 移除斷開的連接
                for ws in disconnected:
                    websocket_connections.remove(ws)
            
            await asyncio.sleep(20)  # 每20秒更新一次
            
        except Exception as e:
            print(f"數據更新失敗: {e}")
            await asyncio.sleep(30)

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    """主Dashboard頁面"""
    return templates.TemplateResponse("fixed_dashboard.html", {
        "request": request,
        "title": "DyFlow Agent Dashboard"
    })

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端點"""
    await websocket.accept()
    websocket_connections.append(websocket)
    
    try:
        # 發送初始數據
        initial_data = {
            "type": "initial_data",
            "data": {
                "system_status": dashboard_data.system_status,
                "pool_data": dashboard_data.pool_data,
                "positions": dashboard_data.positions,
                "risk_alerts": dashboard_data.risk_alerts,
                "agent_logs": dashboard_data.agent_logs[:10],
                "token_prices": dashboard_data.token_prices,
                "last_update": dashboard_data.last_update.isoformat()
            }
        }
        await websocket.send_text(json.dumps(initial_data, default=str))
        
        # 保持連接
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            if message.get("type") == "force_update":
                # 強制更新數據
                await dashboard_data.update_real_data()
            elif message.get("type") == "agent_command":
                # 處理Agent命令
                await handle_agent_command(websocket, message)
                
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)

async def handle_agent_command(websocket: WebSocket, message: Dict):
    """處理Agent命令"""
    try:
        command = message.get("command")

        if command == "execute_agent":
            agent_name = message.get("agent_name")
            response = {
                "type": "command_response",
                "success": True,
                "message": f"Agent {agent_name} 執行完成"
            }
        elif command == "emergency_exit":
            response = {
                "type": "command_response",
                "success": True,
                "message": "緊急退出指令已發送，正在關閉所有LP持倉"
            }
        elif command == "evaluate_pool":
            pool_data = message.get("pool_data", {})
            pool_pair = pool_data.get("pair", "Unknown")
            pool_chain = pool_data.get("chain", "unknown")

            # 模擬Agent評估過程
            evaluation_result = await evaluate_pool_with_agent(pool_data)

            response = {
                "type": "command_response",
                "success": True,
                "message": f"Agent已完成 {pool_pair} ({pool_chain.upper()}) 池子評估",
                "evaluation": evaluation_result
            }

            # 添加評估日誌到dashboard_data
            dashboard_data.agent_logs.insert(0, {
                "timestamp": datetime.now(),
                "agent": "PoolEvaluator",
                "level": "success" if evaluation_result["recommended"] else "warning",
                "message": f"池子 {pool_pair} 評估完成 - {'推薦投資' if evaluation_result['recommended'] else '不推薦投資'}"
            })

        else:
            response = {
                "type": "command_response",
                "success": False,
                "message": f"未知命令: {command}"
            }

        await websocket.send_text(json.dumps(response))

    except Exception as e:
        error_response = {
            "type": "command_response",
            "success": False,
            "message": f"命令執行失敗: {str(e)}"
        }
        await websocket.send_text(json.dumps(error_response))

async def evaluate_pool_with_agent(pool_data: Dict) -> Dict:
    """使用Agent評估池子"""
    try:
        tvl = pool_data.get("tvl_usd", 0)
        apr = pool_data.get("apr", 0)
        risk_level = pool_data.get("risk_level", "high")
        chain = pool_data.get("chain", "unknown")

        # Agent評估邏輯
        score = 0
        reasons = []

        # TVL評估
        if tvl >= 1000000:
            score += 30
            reasons.append("✅ 流動性充足")
        elif tvl >= 100000:
            score += 20
            reasons.append("⚠️ 流動性中等")
        else:
            score += 5
            reasons.append("❌ 流動性不足")

        # APR評估
        if 10 <= apr <= 50:
            score += 25
            reasons.append("✅ APR合理")
        elif 5 <= apr <= 100:
            score += 15
            reasons.append("⚠️ APR需注意")
        else:
            score += 5
            reasons.append("❌ APR異常")

        # 風險評估
        if risk_level == "low":
            score += 25
            reasons.append("✅ 低風險")
        elif risk_level == "medium":
            score += 15
            reasons.append("⚠️ 中等風險")
        else:
            score += 5
            reasons.append("❌ 高風險")

        # 鏈評估
        if chain in ["solana", "bsc"]:
            score += 20
            reasons.append(f"✅ 支援{chain.upper()}鏈")

        recommended = score >= 70

        return {
            "score": score,
            "recommended": recommended,
            "reasons": reasons,
            "recommendation": "推薦投資" if recommended else "不推薦投資",
            "risk_assessment": f"總體風險: {risk_level}"
        }

    except Exception as e:
        return {
            "score": 0,
            "recommended": False,
            "reasons": [f"評估失敗: {str(e)}"],
            "recommendation": "評估失敗",
            "risk_assessment": "無法評估"
        }

@app.get("/api/status")
async def get_system_status():
    """獲取系統狀態API"""
    return {
        "status": "running",
        "last_update": dashboard_data.last_update.isoformat(),
        "system_status": dashboard_data.system_status,
        "pool_count": len(dashboard_data.pool_data),
        "token_prices": dashboard_data.token_prices
    }

if __name__ == "__main__":
    # 集成React UI
    from react_integration import setup_react_integration
    setup_react_integration(app)

    uvicorn.run(
        "modern_app:app",
        host="0.0.0.0",
        port=8082,
        reload=True,
        log_level="info"
    )
