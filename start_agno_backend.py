#!/usr/bin/env python3
"""
DyFlow Agno Backend 启动脚本
测试Agno Framework集成
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_agno_integration():
    """测试Agno Framework集成"""
    print("🚀 测试DyFlow Agno Framework集成...")
    
    try:
        # 测试Agno Framework可用性
        try:
            from agno.agent import Agent
            from agno.tools import Tool
            print("✅ Agno Framework 可用")
            agno_available = True
        except ImportError as e:
            print(f"❌ Agno Framework 不可用: {e}")
            print("💡 使用模拟模式继续测试...")
            agno_available = False
        
        # 测试数据提供代理
        print("\n📊 测试数据提供代理...")
        try:
            from src.agents.data_provider_agent import DataProviderAgent

            # 无论Agno是否可用都尝试初始化
            data_agent = DataProviderAgent()
            print("✅ DataProviderAgent 初始化成功")

            # 测试数据获取
            if agno_available:
                print("  测试实时数据获取...")
                data = await data_agent.get_real_time_pools(['bsc', 'solana'])
                print(f"  ✅ 数据获取成功: BSC {len(data.get('bsc_pools', []))}个, Solana {len(data.get('solana_pools', []))}个")
            else:
                print("  ⚠️ Agno不可用，使用fallback模式")

        except Exception as e:
            print(f"❌ DataProviderAgent 测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 测试池子扫描工具
        print("\n🔍 测试池子扫描工具...")
        try:
            from src.tools.pool_scanner_tool import PoolScannerTool
            
            scanner = PoolScannerTool()
            
            # 测试BSC扫描
            print("  测试BSC池子扫描...")
            bsc_result = await scanner.run('bsc', {'max_pools': 5})
            print(f"  ✅ BSC扫描完成: {len(bsc_result.get('pools', []))}个池子")
            
            # 测试Solana扫描
            print("  测试Solana池子扫描...")
            solana_result = await scanner.run('solana', {'max_pools': 5})
            print(f"  ✅ Solana扫描完成: {len(solana_result.get('pools', []))}个池子")
            
        except Exception as e:
            print(f"❌ 池子扫描测试失败: {e}")
        
        # 测试Supervisor
        print("\n👑 测试Supervisor...")
        try:
            from src.supervisor import DyFlowSupervisor, SupervisorConfig
            from src.utils.config import Config
            
            config = Config()
            supervisor_config = SupervisorConfig(
                enable_agno=agno_available,
                enable_scheduling=False,  # 测试时不启用调度
                enable_monitoring=True
            )
            
            supervisor = DyFlowSupervisor(config, supervisor_config)
            await supervisor.initialize()
            print("✅ DyFlowSupervisor 初始化成功")
            
            # 获取系统状态
            status = supervisor.get_system_status()
            print(f"  📈 系统状态: {status.get('status', 'unknown')}")
            print(f"  🤖 注册的Agents: {len(supervisor.agents)}")
            
        except Exception as e:
            print(f"❌ Supervisor测试失败: {e}")
        
        print("\n🎯 集成测试完成!")
        
        # 如果一切正常，启动后端服务
        if agno_available:
            print("\n🌐 启动Agno后端服务...")
            return True
        else:
            print("\n⚠️ 由于Agno Framework不可用，建议使用原始后端服务")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

async def start_backend_service(use_agno: bool = True):
    """启动后端服务"""
    try:
        if use_agno:
            print("🚀 启动基于Agno Framework的后端服务...")
            import uvicorn
            from dyflow_agno_backend import app
            
            # 启动服务器
            config = uvicorn.Config(
                app=app,
                host="0.0.0.0",
                port=8001,
                log_level="info",
                reload=False
            )
            server = uvicorn.Server(config)
            await server.serve()
            
        else:
            print("🚀 启动原始后端服务...")
            import subprocess
            subprocess.run([sys.executable, "dyflow_real_data_backend.py"])
            
    except Exception as e:
        print(f"❌ 后端服务启动失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 DyFlow Agno Framework 集成测试")
    print("=" * 60)
    
    # 运行集成测试
    use_agno = asyncio.run(test_agno_integration())
    
    print("\n" + "=" * 60)
    
    # 询问用户是否启动服务
    if use_agno:
        choice = input("是否启动Agno后端服务? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            asyncio.run(start_backend_service(use_agno=True))
    else:
        choice = input("是否启动原始后端服务? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            asyncio.run(start_backend_service(use_agno=False))

if __name__ == "__main__":
    main()
