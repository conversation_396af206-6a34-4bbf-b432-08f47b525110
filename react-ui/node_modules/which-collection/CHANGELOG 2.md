# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/which-collection/compare/v1.0.1...v1.0.2) - 2024-03-08

### Commits

- [actions] reuse common workflows [`a5b2949`](https://github.com/inspect-js/which-collection/commit/a5b294901933131cf753c260c0dccf15c1aeeadc)
- [Tests] migrate tests to Github Actions [`283ec03`](https://github.com/inspect-js/which-collection/commit/283ec03d70ad8fdc94b3d77c1b11de011617a04d)
- add types [`bf576db`](https://github.com/inspect-js/which-collection/commit/bf576db80dbc9bca1332622f0b6c4772706dca45)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`24968a2`](https://github.com/inspect-js/which-collection/commit/24968a2aa55109520e2ec0532343224e11a6e311)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`f60b277`](https://github.com/inspect-js/which-collection/commit/f60b27727206261a0359adb5588cba645eb56cf8)
- [Tests] run `nyc` on all tests [`5700269`](https://github.com/inspect-js/which-collection/commit/57002694f9b5f40078bd2777ecc934ece544a556)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`eb1f1a4`](https://github.com/inspect-js/which-collection/commit/eb1f1a468f53bcbed6ddf7459f0811a0f5ea37a7)
- [actions] remove redundant finisher [`cd5b8fc`](https://github.com/inspect-js/which-collection/commit/cd5b8fcddb9d8ea9ea5132b88c50357e2409a277)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`f5786fa`](https://github.com/inspect-js/which-collection/commit/f5786fa3189c884debfa4f160a1733a738acccec)
- [actions] update rebase action to use reusable workflow [`7ac7b67`](https://github.com/inspect-js/which-collection/commit/7ac7b6777797230ca105f9c1560e7b5fc3ba901f)
- [actions] update codecov uploader [`b074105`](https://github.com/inspect-js/which-collection/commit/b074105e5001df42a8889cd8cadaabd2a2fc276c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`ec640d6`](https://github.com/inspect-js/which-collection/commit/ec640d667a48428aec41e19769ae9490da8301e1)
- [actions] add "Allow Edits" workflow [`65d842e`](https://github.com/inspect-js/which-collection/commit/65d842ee9c09ed60370cf14a721b94f9a5de43cd)
- [readme] remove travis badge [`f106289`](https://github.com/inspect-js/which-collection/commit/f10628946ad70286f503497850f6dd2350311577)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `npmignore`, `object-inspect`, `tape` [`e784a1f`](https://github.com/inspect-js/which-collection/commit/e784a1fe96f8bc3eb21350ec7a35c3cf24c66a35)
- [meta] use `npmignore` to autogenerate an npmignore file [`95b503f`](https://github.com/inspect-js/which-collection/commit/95b503fd95f30deca71415da97937a3620e24912)
- [readme] add actions and codecov badges [`121ba2d`](https://github.com/inspect-js/which-collection/commit/121ba2d320ecefd51e4af06cacae2f71dd51b59d)
- [Deps] update `is-map`, `is-set`, `is-weakmap`, `is-weakset` [`4aa150f`](https://github.com/inspect-js/which-collection/commit/4aa150fdad7b8c0728972d6b074cb43464681e97)
- [meta] simplify "exports" [`2afaed7`](https://github.com/inspect-js/which-collection/commit/2afaed7f8918aa87a380c333944db274670838ae)
- [Deps] update `is-map`, `is-set` [`ce44763`](https://github.com/inspect-js/which-collection/commit/ce447638a94303f0571b4c3d12389c20a61d7817)
- [actions] switch Automatic Rease workflow to `pull_request_target` event [`b16b664`](https://github.com/inspect-js/which-collection/commit/b16b6641301c3c9ef7df8000be13a69b8087474f)
- [Dev Deps] update `auto-changelog`, `tape` [`2b9c953`](https://github.com/inspect-js/which-collection/commit/2b9c953b469d7fd0e86d96b89f20ef0c424e4ba1)
- [meta] add missing `engines.node` [`dcdbfde`](https://github.com/inspect-js/which-collection/commit/dcdbfdecc9f77daf99735d1f5377d9f3894fead3)
- [Dev Deps] update `auto-changelog`; add `aud` [`8e7f28d`](https://github.com/inspect-js/which-collection/commit/8e7f28d82f6240b2eaf763c32a3ede53be6cdfe1)
- [meta] add `sideEffects` flag [`3e0376b`](https://github.com/inspect-js/which-collection/commit/3e0376b80d7a18b78fdc898a9a166e9ffc83eee3)
- [Deps] update `is-weakset` [`7b3e922`](https://github.com/inspect-js/which-collection/commit/7b3e922ca0f9f356c1cbf4701857d71b378eb7d7)
- [Tests] only audit prod deps [`3339fea`](https://github.com/inspect-js/which-collection/commit/3339fea827a7fdcf8db868bb52278a3186593d48)

## [v1.0.1](https://github.com/inspect-js/which-collection/compare/v1.0.0...v1.0.1) - 2020-01-26

### Commits

- [actions] add automatic rebasing / merge commit blocking [`c3820b2`](https://github.com/inspect-js/which-collection/commit/c3820b2e8c88548f2c7da4080b1d1b6b41be97a4)
- [patch] add "exports" [`10983b5`](https://github.com/inspect-js/which-collection/commit/10983b5fdcc453c64216c3d6aa3fb93340091818)
- [Deps] update `is-map`, `is-set`, `is-weakmap`, `is-weakset` [`1565925`](https://github.com/inspect-js/which-collection/commit/1565925705c4abfe88065b211d1d960791f7cd3c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`92ef871`](https://github.com/inspect-js/which-collection/commit/92ef871338395352f1bafc3156088361a3fd917a)
- [Dev Deps] update `@ljharb/eslint-config` [`61e9cde`](https://github.com/inspect-js/which-collection/commit/61e9cde1830ccc2b551dd6a1a873ae2cf27a74c7)

## v1.0.0 - 2019-11-13

### Commits

- Initial commit [`a21fddf`](https://github.com/inspect-js/which-collection/commit/a21fddffef3b2f21923e4d056295dd63661d8155)
- Tests [`ec86bc1`](https://github.com/inspect-js/which-collection/commit/ec86bc12f0516bd662c6e2966b36de2e1128a431)
- readme [`ffe969c`](https://github.com/inspect-js/which-collection/commit/ffe969cf4388d18e12c664cc51498bbdef08e565)
- implementation [`9acb669`](https://github.com/inspect-js/which-collection/commit/9acb6695e6a5e60f4c0b6de59eaf8b1f681d78e5)
- npm init [`124a63e`](https://github.com/inspect-js/which-collection/commit/124a63ee68a0015b47cbcc08b0d5598e553e7c9a)
- [meta] add `auto-changelog`, `safe-publish-latest` [`df0d6d4`](https://github.com/inspect-js/which-collection/commit/df0d6d4f1efbc4d9b327471b9c659bd487b25b49)
- [meta] add `funding` field; create FUNDING.yml [`032c81c`](https://github.com/inspect-js/which-collection/commit/032c81c826d68acd6242fa87fd6348db70135506)
- [Tests] add `npm run lint` [`6ae406d`](https://github.com/inspect-js/which-collection/commit/6ae406d9e459779abbdd90f48559552f740b05c9)
- fixup [`a2cad36`](https://github.com/inspect-js/which-collection/commit/a2cad363f12e30afe7619597187c5d4dc840a2a7)
- Only apps should have lockfiles [`30b3aae`](https://github.com/inspect-js/which-collection/commit/30b3aae37155f0786e4582501369f738b3282cd7)
