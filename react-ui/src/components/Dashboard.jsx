import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from './ui/tabs'
import { Activity, TrendingUp, DollarSign, Zap, ExternalLink, RefreshCw, Wifi, WifiOff, Bot, AlertTriangle } from 'lucide-react'
import { formatCurrency, formatPercentage } from '../lib/utils'
import { useWebSocket } from '../contexts/WebSocketContext'

const Dashboard = () => {
  const {
    isConnected,
    connectionStatus,
    dashboardData,
    forceUpdate,
    evaluatePool,
    emergencyExit
  } = useWebSocket()

  const [activeTab, setActiveTab] = useState('overview')

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  }

  const handlePoolEvaluate = (pool) => {
    evaluatePool(pool)
  }

  const handleEmergencyExit = () => {
    if (window.confirm('確定要執行緊急退出嗎？這將關閉所有LP持倉。')) {
      emergencyExit()
    }
  }

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500'
      case 'connecting': return 'text-yellow-500'
      case 'disconnected': return 'text-gray-500'
      case 'error': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return '已連接'
      case 'connecting': return '連接中...'
      case 'disconnected': return '未連接'
      case 'error': return '連接錯誤'
      default: return '未知狀態'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-7xl mx-auto space-y-6"
      >
        {/* Header with Connection Status */}
        <motion.div variants={itemVariants} className="flex justify-between items-center mb-8">
          <div className="text-center flex-1">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              DyFlow Dashboard
            </h1>
            <p className="text-gray-600">
              24/7 自動化流動性挖礦策略系統
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className={`h-5 w-5 ${getConnectionStatusColor()}`} />
              ) : (
                <WifiOff className={`h-5 w-5 ${getConnectionStatusColor()}`} />
              )}
              <span className={`text-sm ${getConnectionStatusColor()}`}>
                {getConnectionStatusText()}
              </span>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={forceUpdate}
              disabled={!isConnected}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              刷新
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={handleEmergencyExit}
              disabled={!isConnected}
            >
              <AlertTriangle className="h-4 w-4 mr-1" />
              緊急退出
            </Button>
          </div>
        </motion.div>

        {/* System Status Cards */}
        <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Meteora API</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={dashboardData.system_status.connections.meteora_api === "connected" ? "default" : "destructive"}>
                {dashboardData.system_status.connections.meteora_api === "connected" ? "已連接" : "未連接"}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">PancakeSwap API</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={dashboardData.system_status.connections.pancakeswap_api === "connected" ? "default" : "destructive"}>
                {dashboardData.system_status.connections.pancakeswap_api === "connected" ? "已連接" : "未連接"}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CoinGecko API</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={dashboardData.system_status.connections.coingecko_api === "connected" ? "default" : "destructive"}>
                {dashboardData.system_status.connections.coingecko_api === "connected" ? "已連接" : "未連接"}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">總池子數</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardData.pool_data.length}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Agent狀態</CardTitle>
              <Bot className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={dashboardData.system_status.supervisor.is_running ? "default" : "destructive"}>
                {dashboardData.system_status.supervisor.is_running ? "運行中" : "已停止"}
              </Badge>
            </CardContent>
          </Card>
        </motion.div>

        {/* Main Content Tabs */}
        <motion.div variants={itemVariants}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">系統概覽</TabsTrigger>
              <TabsTrigger value="pools">流動性池子</TabsTrigger>
              <TabsTrigger value="positions">LP持倉</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Agent Logs */}
                <Card>
                  <CardHeader>
                    <CardTitle>Agent日誌</CardTitle>
                    <CardDescription>最新的Agent活動記錄</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {dashboardData.agent_logs.map((log, index) => (
                        <div key={index} className="flex items-start space-x-2 text-sm">
                          <span className="text-gray-500 text-xs whitespace-nowrap">
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </span>
                          <Badge
                            variant={
                              log.level === 'error' ? 'destructive' :
                              log.level === 'warning' ? 'secondary' : 'default'
                            }
                            className="text-xs"
                          >
                            {log.agent}
                          </Badge>
                          <span className="flex-1">{log.message}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Token Prices */}
                <Card>
                  <CardHeader>
                    <CardTitle>代幣價格</CardTitle>
                    <CardDescription>實時代幣價格信息</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      {Object.entries(dashboardData.token_prices || {}).map(([token, price]) => (
                        <div key={token} className="flex justify-between items-center">
                          <span className="font-medium">{token}</span>
                          <span className="text-green-600">
                            ${typeof price === 'number' ? price.toFixed(2) : parseFloat(price || 0).toFixed(2)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="pools" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>流動性池子</CardTitle>
                  <CardDescription>
                    實時監控的高收益LP池子 ({dashboardData.pool_data.length} 個池子)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">交易對</th>
                          <th className="text-left p-2">網絡</th>
                          <th className="text-left p-2">TVL</th>
                          <th className="text-left p-2">APR</th>
                          <th className="text-left p-2">24h 手續費</th>
                          <th className="text-left p-2">風險等級</th>
                          <th className="text-left p-2">操作</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.pool_data.map((pool, index) => (
                          <motion.tr
                            key={pool.id || index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="p-2 font-medium">{pool.pair}</td>
                            <td className="p-2">
                              <Badge variant={pool.chain === 'bsc' ? 'secondary' : 'default'}>
                                {pool.chain === 'bsc' ? 'BSC' : 'SOL'}
                              </Badge>
                            </td>
                            <td className="p-2">{formatCurrency(pool.tvl_usd)}</td>
                            <td className="p-2 text-green-600 font-semibold">
                              {formatPercentage(pool.apr)}
                            </td>
                            <td className="p-2">{formatCurrency(pool.fees_24h)}</td>
                            <td className="p-2">
                              <Badge
                                variant={
                                  pool.risk_level === 'low' ? 'default' :
                                  pool.risk_level === 'medium' ? 'secondary' : 'destructive'
                                }
                              >
                                {pool.risk_level === 'low' ? '低風險' :
                                 pool.risk_level === 'medium' ? '中風險' : '高風險'}
                              </Badge>
                            </td>
                            <td className="p-2 space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(pool.pancake_url || pool.meteora_url, '_blank')}
                              >
                                <ExternalLink className="h-4 w-4 mr-1" />
                                查看
                              </Button>
                              <Button
                                size="sm"
                                variant="default"
                                onClick={() => handlePoolEvaluate(pool)}
                                disabled={!isConnected}
                              >
                                <Bot className="h-4 w-4 mr-1" />
                                評估
                              </Button>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="positions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>LP持倉</CardTitle>
                  <CardDescription>
                    當前活躍的流動性挖礦持倉 ({dashboardData.positions.length} 個持倉)
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-2">池子</th>
                          <th className="text-left p-2">網絡</th>
                          <th className="text-left p-2">流動性</th>
                          <th className="text-left p-2">PnL</th>
                          <th className="text-left p-2">IL</th>
                          <th className="text-left p-2">APR</th>
                          <th className="text-left p-2">狀態</th>
                        </tr>
                      </thead>
                      <tbody>
                        {dashboardData.positions.map((position, index) => (
                          <motion.tr
                            key={position.id || index}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="border-b hover:bg-gray-50"
                          >
                            <td className="p-2 font-medium">{position.pool}</td>
                            <td className="p-2">
                              <Badge variant={position.chain === 'bsc' ? 'secondary' : 'default'}>
                                {position.chain === 'bsc' ? 'BSC' : 'SOL'}
                              </Badge>
                            </td>
                            <td className="p-2">{formatCurrency(position.liquidity_usd)}</td>
                            <td className={`p-2 font-semibold ${position.pnl_pct >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {position.pnl_pct >= 0 ? '+' : ''}{formatPercentage(position.pnl_pct)}
                            </td>
                            <td className={`p-2 font-semibold ${position.il_pct >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatPercentage(position.il_pct)}
                            </td>
                            <td className="p-2 text-green-600 font-semibold">
                              {formatPercentage(position.apr)}
                            </td>
                            <td className="p-2">
                              <Badge variant={position.status === 'active' ? 'default' : 'secondary'}>
                                {position.status === 'active' ? '活躍' : '監控中'}
                              </Badge>
                            </td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default Dashboard
