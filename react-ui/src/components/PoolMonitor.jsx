import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Eye, TrendingUp, TrendingDown, AlertCircle, CheckCircle, Clock, ExternalLink, Bot } from 'lucide-react'
import { formatCurrency, formatPercentage } from '../lib/utils'

const PoolMonitor = ({ pools, onEvaluatePool }) => {
  const [filter, setFilter] = useState('all') // all, bsc, solana, high-yield
  const [sortBy, setSortBy] = useState('apr') // apr, tvl, fees

  const filteredPools = pools.filter(pool => {
    switch (filter) {
      case 'bsc': return pool.chain === 'bsc'
      case 'solana': return pool.chain === 'solana'
      case 'high-yield': return pool.apr > 100
      default: return true
    }
  }).sort((a, b) => {
    switch (sortBy) {
      case 'tvl': return (b.tvl_usd || 0) - (a.tvl_usd || 0)
      case 'fees': return (b.fees_24h || 0) - (a.fees_24h || 0)
      default: return (b.apr || 0) - (a.apr || 0)
    }
  })

  const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'high': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'low': return <CheckCircle className="h-4 w-4" />
      case 'medium': return <Clock className="h-4 w-4" />
      case 'high': return <AlertCircle className="h-4 w-4" />
      default: return <Eye className="h-4 w-4" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Eye className="h-5 w-5 text-blue-500" />
          <span>池子監控面板</span>
        </CardTitle>
        <CardDescription>
          實時監控 {filteredPools.length} 個流動性池子
        </CardDescription>
        
        {/* Filters */}
        <div className="flex flex-wrap gap-2 mt-4">
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant={filter === 'all' ? 'default' : 'outline'}
              onClick={() => setFilter('all')}
            >
              全部 ({pools.length})
            </Button>
            <Button
              size="sm"
              variant={filter === 'bsc' ? 'default' : 'outline'}
              onClick={() => setFilter('bsc')}
            >
              BSC ({pools.filter(p => p.chain === 'bsc').length})
            </Button>
            <Button
              size="sm"
              variant={filter === 'solana' ? 'default' : 'outline'}
              onClick={() => setFilter('solana')}
            >
              Solana ({pools.filter(p => p.chain === 'solana').length})
            </Button>
            <Button
              size="sm"
              variant={filter === 'high-yield' ? 'default' : 'outline'}
              onClick={() => setFilter('high-yield')}
            >
              高收益 ({pools.filter(p => p.apr > 100).length})
            </Button>
          </div>
          
          <div className="flex space-x-2 ml-4">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 border rounded-md text-sm"
            >
              <option value="apr">按APR排序</option>
              <option value="tvl">按TVL排序</option>
              <option value="fees">按手續費排序</option>
            </select>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredPools.map((pool, index) => (
            <motion.div
              key={pool.address || index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-white"
            >
              {/* Pool Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <Badge variant={pool.chain === 'bsc' ? 'secondary' : 'default'}>
                    {pool.chain === 'bsc' ? 'BSC' : 'SOL'}
                  </Badge>
                  <span className="font-semibold text-sm">{pool.pair}</span>
                </div>
                <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${getRiskColor(pool.risk_level)}`}>
                  {getRiskIcon(pool.risk_level)}
                  <span className="text-xs font-medium">
                    {pool.risk_level === 'low' ? '低風險' :
                     pool.risk_level === 'medium' ? '中風險' : '高風險'}
                  </span>
                </div>
              </div>

              {/* Pool Metrics */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">APR</span>
                  <div className="flex items-center space-x-1">
                    {pool.apr > 100 ? (
                      <TrendingUp className="h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-gray-400" />
                    )}
                    <span className="font-bold text-green-600">
                      {formatPercentage(pool.apr)}
                    </span>
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">TVL</span>
                  <span className="font-semibold text-blue-600">
                    {formatCurrency(pool.tvl_usd)}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">24h手續費</span>
                  <span className="font-semibold text-purple-600">
                    {formatCurrency(pool.fees_24h)}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => window.open(pool.pancake_url || pool.meteora_url, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  查看
                </Button>
                <Button
                  size="sm"
                  variant="default"
                  className="flex-1"
                  onClick={() => onEvaluatePool(pool)}
                >
                  <Bot className="h-3 w-3 mr-1" />
                  評估
                </Button>
              </div>
            </motion.div>
          ))}
        </div>
        
        {filteredPools.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Eye className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>沒有找到符合條件的池子</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default PoolMonitor
