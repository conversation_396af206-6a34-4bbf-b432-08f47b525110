import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Eye, TrendingUp, TrendingDown, AlertCircle, CheckCircle, Clock, ExternalLink, Bot } from 'lucide-react'
import { formatCurrency, formatPercentage } from '../lib/utils'

const PoolMonitor = ({ pools, onEvaluatePool }) => {
  const [sortBy, setSortBy] = useState('apr') // apr, tvl, fees

  // 分离BSC和Solana池子
  const bscPools = pools.filter(pool => pool.chain === 'bsc').sort((a, b) => {
    switch (sortBy) {
      case 'tvl': return (b.tvl_usd || 0) - (a.tvl_usd || 0)
      case 'fees': return (b.fees_24h || 0) - (a.fees_24h || 0)
      default: return (b.apr || 0) - (a.apr || 0)
    }
  })

  const solanaPools = pools.filter(pool => pool.chain === 'solana').sort((a, b) => {
    switch (sortBy) {
      case 'tvl': return (b.tvl_usd || 0) - (a.tvl_usd || 0)
      case 'fees': return (b.fees_24h || 0) - (a.fees_24h || 0)
      default: return (b.apr || 0) - (a.apr || 0)
    }
  })

  const getRiskColor = (riskLevel) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'high': return 'text-red-600 bg-red-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getRiskIcon = (riskLevel) => {
    switch (riskLevel) {
      case 'low': return <CheckCircle className="h-4 w-4" />
      case 'medium': return <Clock className="h-4 w-4" />
      case 'high': return <AlertCircle className="h-4 w-4" />
      default: return <Eye className="h-4 w-4" />
    }
  }

  const PoolTable = ({ pools, title, chainBadgeVariant, chainLabel }) => (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Badge variant={chainBadgeVariant} className="text-sm">
            {chainLabel}
          </Badge>
          <span>{title}</span>
          <span className="text-sm text-gray-500">({pools.length} 個池子)</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left p-3 font-medium">交易對</th>
                <th className="text-left p-3 font-medium">TVL</th>
                <th className="text-left p-3 font-medium">APR</th>
                <th className="text-left p-3 font-medium">24h手續費</th>
                <th className="text-left p-3 font-medium">風險等級</th>
                <th className="text-left p-3 font-medium">操作</th>
              </tr>
            </thead>
            <tbody>
              {pools.map((pool, index) => (
                <motion.tr
                  key={pool.address || index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.02 }}
                  className="border-b hover:bg-gray-50 transition-colors"
                >
                  <td className="p-3">
                    <div className="flex items-center space-x-2">
                      <span className="font-semibold">{pool.pair}</span>
                      {pool.apr > 100 && (
                        <Badge variant="destructive" className="text-xs">
                          🔥 高收益
                        </Badge>
                      )}
                    </div>
                  </td>
                  <td className="p-3">
                    <span className="font-semibold text-blue-600">
                      {formatCurrency(pool.tvl_usd)}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className="flex items-center space-x-1">
                      {pool.apr > 100 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-gray-400" />
                      )}
                      <span className="font-bold text-green-600">
                        {formatPercentage(pool.apr)}
                      </span>
                    </div>
                  </td>
                  <td className="p-3">
                    <span className="font-semibold text-purple-600">
                      {formatCurrency(pool.fees_24h)}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className={`flex items-center space-x-1 px-2 py-1 rounded-full ${getRiskColor(pool.risk_level)}`}>
                      {getRiskIcon(pool.risk_level)}
                      <span className="text-xs font-medium">
                        {pool.risk_level === 'low' ? '低風險' :
                         pool.risk_level === 'medium' ? '中風險' : '高風險'}
                      </span>
                    </div>
                  </td>
                  <td className="p-3">
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => window.open(pool.pancake_url || pool.meteora_url, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3 mr-1" />
                        查看
                      </Button>
                      <Button
                        size="sm"
                        variant="default"
                        onClick={() => onEvaluatePool(pool)}
                      >
                        <Bot className="h-3 w-3 mr-1" />
                        評估
                      </Button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {pools.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Eye className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>沒有找到池子</p>
          </div>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="h-5 w-5 text-blue-500" />
            <span>池子監控面板</span>
          </CardTitle>
          <CardDescription>
            實時監控 {pools.length} 個流動性池子 (BSC: {bscPools.length}, Solana: {solanaPools.length})
          </CardDescription>

          {/* Sort Controls */}
          <div className="flex items-center space-x-4 mt-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">排序方式:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm bg-white"
              >
                <option value="apr">按APR排序</option>
                <option value="tvl">按TVL排序</option>
                <option value="fees">按24h手續費排序</option>
              </select>
            </div>

            {/* Summary Stats */}
            <div className="flex space-x-4 text-sm text-gray-600">
              <span>高收益池: {pools.filter(p => p.apr > 100).length}</span>
              <span>總TVL: {formatCurrency(pools.reduce((sum, p) => sum + (p.tvl_usd || 0), 0))}</span>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* BSC Pools Table */}
      <PoolTable
        pools={bscPools}
        title="BSC 流動性池子"
        chainBadgeVariant="secondary"
        chainLabel="BSC"
      />

      {/* Solana Pools Table */}
      <PoolTable
        pools={solanaPools}
        title="Solana 流動性池子"
        chainBadgeVariant="default"
        chainLabel="SOL"
      />
    </div>
  )
}

export default PoolMonitor
