import React from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { Activity, TrendingUp, DollarSign, Zap, Globe, Database, Shield, Target, AlertTriangle, CheckCircle } from 'lucide-react'
import { formatCurrency, formatPercentage } from '../lib/utils'

const SystemOverview = ({ dashboardData }) => {
  const totalTVL = dashboardData.pool_data.reduce((sum, p) => sum + (p.tvl_usd || 0), 0)
  const totalFees = dashboardData.pool_data.reduce((sum, p) => sum + (p.fees_24h || 0), 0)
  const avgAPR = dashboardData.pool_data.length > 0 
    ? dashboardData.pool_data.reduce((sum, p) => sum + p.apr, 0) / dashboardData.pool_data.length 
    : 0

  const bscPools = dashboardData.pool_data.filter(p => p.chain === 'bsc')
  const solanaPools = dashboardData.pool_data.filter(p => p.chain === 'solana')
  const highYieldPools = dashboardData.pool_data.filter(p => p.apr > 100)

  const systemHealth = {
    overall: 95,
    apis: 98,
    agents: 92,
    monitoring: 97
  }

  const getHealthColor = (score) => {
    if (score >= 95) return 'text-green-600'
    if (score >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getHealthIcon = (score) => {
    if (score >= 95) return <CheckCircle className="h-4 w-4 text-green-500" />
    if (score >= 80) return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    return <AlertTriangle className="h-4 w-4 text-red-500" />
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">總TVL</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(totalTVL)}</div>
            <p className="text-xs text-muted-foreground">
              跨 {dashboardData.pool_data.length} 個池子
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">24h 總手續費</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(totalFees)}</div>
            <p className="text-xs text-muted-foreground">
              日收益潛力
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均APR</CardTitle>
            <Activity className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">{formatPercentage(avgAPR)}</div>
            <p className="text-xs text-muted-foreground">
              {highYieldPools.length} 個高收益池
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">系統健康度</CardTitle>
            {getHealthIcon(systemHealth.overall)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthColor(systemHealth.overall)}`}>
              {systemHealth.overall}%
            </div>
            <p className="text-xs text-muted-foreground">
              所有系統正常
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Network Distribution */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5 text-blue-500" />
            <span>網絡分佈</span>
          </CardTitle>
          <CardDescription>跨鏈池子分佈與性能</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* BSC Stats */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">BSC</Badge>
                  <span className="font-medium">Binance Smart Chain</span>
                </div>
                <span className="text-sm text-gray-500">{bscPools.length} 個池子</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>TVL</span>
                  <span className="font-semibold">
                    {formatCurrency(bscPools.reduce((sum, p) => sum + (p.tvl_usd || 0), 0))}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>平均APR</span>
                  <span className="font-semibold text-green-600">
                    {bscPools.length > 0 ? formatPercentage(bscPools.reduce((sum, p) => sum + p.apr, 0) / bscPools.length) : '0%'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>24h手續費</span>
                  <span className="font-semibold">
                    {formatCurrency(bscPools.reduce((sum, p) => sum + (p.fees_24h || 0), 0))}
                  </span>
                </div>
              </div>
            </div>

            {/* Solana Stats */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="default">SOL</Badge>
                  <span className="font-medium">Solana</span>
                </div>
                <span className="text-sm text-gray-500">{solanaPools.length} 個池子</span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>TVL</span>
                  <span className="font-semibold">
                    {formatCurrency(solanaPools.reduce((sum, p) => sum + (p.tvl_usd || 0), 0))}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>平均APR</span>
                  <span className="font-semibold text-green-600">
                    {solanaPools.length > 0 ? formatPercentage(solanaPools.reduce((sum, p) => sum + p.apr, 0) / solanaPools.length) : '0%'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>24h手續費</span>
                  <span className="font-semibold">
                    {formatCurrency(solanaPools.reduce((sum, p) => sum + (p.fees_24h || 0), 0))}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* System Health Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-green-500" />
            <span>系統健康狀態</span>
          </CardTitle>
          <CardDescription>各子系統運行狀態詳情</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Globe className="h-4 w-4 text-blue-500" />
                <span className="font-medium">API 連接</span>
              </div>
              <div className="flex items-center space-x-2">
                <Progress value={systemHealth.apis} className="w-24" />
                <span className={`text-sm font-semibold ${getHealthColor(systemHealth.apis)}`}>
                  {systemHealth.apis}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Target className="h-4 w-4 text-green-500" />
                <span className="font-medium">AI Agents</span>
              </div>
              <div className="flex items-center space-x-2">
                <Progress value={systemHealth.agents} className="w-24" />
                <span className={`text-sm font-semibold ${getHealthColor(systemHealth.agents)}`}>
                  {systemHealth.agents}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Database className="h-4 w-4 text-purple-500" />
                <span className="font-medium">數據監控</span>
              </div>
              <div className="flex items-center space-x-2">
                <Progress value={systemHealth.monitoring} className="w-24" />
                <span className={`text-sm font-semibold ${getHealthColor(systemHealth.monitoring)}`}>
                  {systemHealth.monitoring}%
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SystemOverview
