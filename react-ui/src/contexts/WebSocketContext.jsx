import React, { createContext, useContext, useEffect, useState, useRef } from 'react'

const WebSocketContext = createContext()

export const useWebSocket = () => {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
}

export const WebSocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [dashboardData, setDashboardData] = useState({
    system_status: {
      supervisor: { is_running: false, agno_enabled: false, uptime_seconds: 0 },
      connections: {
        meteora_api: "disconnected",
        pancakeswap_api: "disconnected",
        coingecko_api: "disconnected"
      }
    },
    pool_data: [],
    positions: [],
    risk_alerts: [],
    agent_logs: [],
    token_prices: {},
    last_update: new Date().toISOString()
  })
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  const connectWebSocket = () => {
    try {
      // 根據當前環境確定WebSocket URL
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const host = window.location.hostname
      const port = window.location.port || (window.location.protocol === 'https:' ? '443' : '80')
      
      // 如果是開發環境，連接到FastAPI後端
      const wsUrl = process.env.NODE_ENV === 'development' 
        ? 'ws://localhost:8082/ws'
        : `${protocol}//${host}:${port}/ws`

      console.log('🔌 正在連接WebSocket:', wsUrl)
      setConnectionStatus('connecting')

      const ws = new WebSocket(wsUrl)

      ws.onopen = () => {
        console.log('✅ WebSocket連接成功')
        setSocket(ws)
        setIsConnected(true)
        setConnectionStatus('connected')
        reconnectAttempts.current = 0
      }

      ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('📨 收到WebSocket消息:', message.type)

          switch (message.type) {
            case 'initial_data':
            case 'dashboard_update':
              setDashboardData(message.data)
              break
            case 'command_response':
              console.log('🤖 Agent命令響應:', message)
              // 可以在這裡處理命令響應
              break
            default:
              console.log('❓ 未知消息類型:', message.type)
          }
        } catch (error) {
          console.error('❌ 解析WebSocket消息失敗:', error)
        }
      }

      ws.onclose = (event) => {
        console.log('🔌 WebSocket連接關閉:', event.code, event.reason)
        setSocket(null)
        setIsConnected(false)
        setConnectionStatus('disconnected')

        // 自動重連邏輯
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000)
          console.log(`🔄 ${delay/1000}秒後嘗試重連 (${reconnectAttempts.current + 1}/${maxReconnectAttempts})`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++
            connectWebSocket()
          }, delay)
        } else {
          console.log('❌ 達到最大重連次數，停止重連')
          setConnectionStatus('failed')
        }
      }

      ws.onerror = (error) => {
        console.error('❌ WebSocket錯誤:', error)
        setConnectionStatus('error')
      }

    } catch (error) {
      console.error('❌ 創建WebSocket連接失敗:', error)
      setConnectionStatus('error')
    }
  }

  const sendMessage = (message) => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(JSON.stringify(message))
      console.log('📤 發送WebSocket消息:', message.type)
    } else {
      console.warn('⚠️ WebSocket未連接，無法發送消息')
    }
  }

  const forceUpdate = () => {
    sendMessage({ type: 'force_update' })
  }

  const executeAgentCommand = (command, data = {}) => {
    sendMessage({
      type: 'agent_command',
      command,
      ...data
    })
  }

  const evaluatePool = (poolData) => {
    sendMessage({
      type: 'agent_command',
      command: 'evaluate_pool',
      pool_data: poolData
    })
  }

  const emergencyExit = () => {
    sendMessage({
      type: 'agent_command',
      command: 'emergency_exit'
    })
  }

  useEffect(() => {
    connectWebSocket()

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
      if (socket) {
        socket.close()
      }
    }
  }, [])

  const value = {
    socket,
    isConnected,
    connectionStatus,
    dashboardData,
    sendMessage,
    forceUpdate,
    executeAgentCommand,
    evaluatePool,
    emergencyExit,
    reconnect: connectWebSocket
  }

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}
