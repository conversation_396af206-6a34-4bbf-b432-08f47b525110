#!/usr/bin/env python3
"""
测试Agent间通信和前端显示
验证Agno Framework集成是否正常工作
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime
import structlog

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

async def test_data_provider_agent():
    """测试DataProviderAgent"""
    print("🔍 测试DataProviderAgent...")
    
    try:
        from src.agents.data_provider_agent import DataProviderAgent
        
        # 创建Agent实例
        agent = DataProviderAgent()
        print("✅ DataProviderAgent创建成功")
        
        # 测试execute方法 (Agno Framework调度器使用)
        print("  测试execute方法...")
        result = await agent.execute("获取BSC和Solana池子数据")
        
        print(f"  ✅ execute方法执行成功")
        print(f"  📊 状态: {result.get('status')}")
        print(f"  📊 BSC池子: {result.get('summary', {}).get('bsc_pools', 0)}个")
        print(f"  📊 Solana池子: {result.get('summary', {}).get('solana_pools', 0)}个")
        
        return result
        
    except Exception as e:
        print(f"❌ DataProviderAgent测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_agno_scheduler():
    """测试Agno调度器"""
    print("\n👑 测试DyFlowAgnoScheduler...")
    
    try:
        from src.core.dyflow_agno_scheduler import DyFlowAgnoScheduler
        from src.utils.config import Config
        
        # 创建配置和调度器
        config = Config()
        scheduler = DyFlowAgnoScheduler(config)
        print("✅ DyFlowAgnoScheduler创建成功")
        
        # 获取系统状态
        status = scheduler.get_system_status()
        print(f"  📊 调度器运行状态: {status.get('running')}")
        print(f"  📊 注册的Agents: {len(status.get('agents', {}))}")
        
        # 列出所有Agent
        for agent_name, agent_info in status.get('agents', {}).items():
            print(f"    - {agent_name}: {agent_info.get('state', 'unknown')}")
        
        return status
        
    except Exception as e:
        print(f"❌ DyFlowAgnoScheduler测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_agent_communication():
    """测试Agent间通信"""
    print("\n🔄 测试Agent间通信...")
    
    try:
        from src.core.dyflow_agno_scheduler import DyFlowAgnoScheduler
        from src.utils.config import Config
        
        # 创建调度器
        config = Config()
        scheduler = DyFlowAgnoScheduler(config)
        
        # 测试消息发送
        print("  测试消息发送...")
        scheduler.send_message(
            from_agent="data_provider",
            to_agent="planner",
            msg_type="pool_data_update",
            data={
                "bsc_pools": 20,
                "solana_pools": 31,
                "high_apr_pools": 5
            }
        )
        
        print("  ✅ 消息发送成功")
        print(f"  📊 消息队列大小: {len(scheduler.message_queue)}")
        
        # 处理消息队列
        await scheduler._process_message_queue()
        print("  ✅ 消息队列处理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent通信测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        import aiohttp
        
        # 测试Agent状态API
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get('http://localhost:8001/api/agents/status') as response:
                    if response.status == 200:
                        data = await response.json()
                        print("  ✅ Agent状态API正常")
                        print(f"  📊 返回的Agents: {len(data.get('agents', {}))}")
                        
                        # 显示Agent状态
                        for agent_name, agent_info in data.get('agents', {}).items():
                            print(f"    - {agent_name}: {agent_info.get('status')} (成功率: {agent_info.get('success_rate')}%)")
                        
                        return data
                    else:
                        print(f"  ❌ Agent状态API错误: {response.status}")
                        return None
                        
            except Exception as e:
                print(f"  ❌ 无法连接到API: {e}")
                print("  💡 请确保后端服务正在运行: python dyflow_real_data_backend.py")
                return None
                
    except ImportError:
        print("  ❌ aiohttp未安装，跳过API测试")
        return None

async def test_frontend_integration():
    """测试前端集成"""
    print("\n🎨 测试前端集成...")
    
    try:
        # 检查React UI是否运行
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get('http://localhost:3000') as response:
                    if response.status == 200:
                        print("  ✅ React UI正在运行")
                        print("  🌐 访问地址: http://localhost:3000")
                        return True
                    else:
                        print(f"  ❌ React UI响应错误: {response.status}")
                        return False
                        
            except Exception as e:
                print(f"  ❌ 无法连接到React UI: {e}")
                print("  💡 请启动React UI: cd react-ui && npm run dev")
                return False
                
    except ImportError:
        print("  ❌ aiohttp未安装，跳过前端测试")
        return False

async def main():
    """主测试函数"""
    print("=" * 80)
    print("🎯 DyFlow Agent通信和前端显示测试")
    print("=" * 80)
    
    # 执行所有测试
    tests = [
        ("DataProviderAgent", test_data_provider_agent),
        ("Agno调度器", test_agno_scheduler),
        ("Agent间通信", test_agent_communication),
        ("API端点", test_api_endpoints),
        ("前端集成", test_frontend_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result is not None and result is not False))
        except Exception as e:
            logger.error("test_execution_failed", test=test_name, error=str(e))
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 测试结果总结")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        status_icon = "✅" if result else "❌"
        print(f"{status_icon} {test_name}: {'通过' if result else '失败'}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！Agent通信和前端显示正常工作")
        print("\n💡 下一步:")
        print("1. 访问 http://localhost:3000 查看React UI")
        print("2. 点击 'Agent状态' 标签查看Agent监控")
        print("3. 观察实时数据更新和Agent活动日志")
    else:
        print("\n⚠️ 部分测试失败，请检查:")
        print("1. 后端服务是否运行: python dyflow_real_data_backend.py")
        print("2. React UI是否运行: cd react-ui && npm run dev")
        print("3. 所有依赖是否安装正确")

if __name__ == "__main__":
    asyncio.run(main())
